#!/usr/bin/env python3
"""
照片标记删除功能的集成测试脚本
用于验证整个功能的端到端工作流程
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

from photo_dedup.database import initialize_database
from photo_dedup.collector import (
    mark_photo_for_deletion,
    unmark_photo_for_deletion,
    get_photo_mark_status,
    get_marked_photos_summary
)


async def test_photo_marking_workflow():
    """测试照片标记工作流程"""
    print("🧪 开始照片标记删除功能集成测试...")
    
    try:
        # 初始化数据库
        print("📊 初始化数据库...")
        Session = initialize_database()
        
        # 测试照片UUID（使用一个假的UUID进行测试）
        test_photo_uuid = "test-integration-uuid-12345"
        
        print(f"🔍 测试照片UUID: {test_photo_uuid}")
        
        # 1. 检查初始状态
        print("\n1️⃣ 检查照片初始标记状态...")
        initial_status = get_photo_mark_status(test_photo_uuid)
        print(f"   初始标记状态: {initial_status}")
        
        # 2. 获取初始摘要
        print("\n2️⃣ 获取初始已标记照片摘要...")
        initial_summary = get_marked_photos_summary()
        print(f"   初始已标记照片数量: {initial_summary['marked_count']}")
        
        # 3. 标记照片删除
        print(f"\n3️⃣ 标记照片 {test_photo_uuid} 为删除...")
        mark_result = mark_photo_for_deletion(test_photo_uuid)
        print(f"   标记结果: {'✅ 成功' if mark_result else '❌ 失败'}")
        
        if mark_result:
            # 4. 验证标记状态
            print("\n4️⃣ 验证照片标记状态...")
            marked_status = get_photo_mark_status(test_photo_uuid)
            print(f"   标记后状态: {marked_status}")
            
            # 5. 获取更新后的摘要
            print("\n5️⃣ 获取更新后的已标记照片摘要...")
            updated_summary = get_marked_photos_summary()
            print(f"   更新后已标记照片数量: {updated_summary['marked_count']}")
            
            # 6. 取消标记
            print(f"\n6️⃣ 取消照片 {test_photo_uuid} 的删除标记...")
            unmark_result = unmark_photo_for_deletion(test_photo_uuid)
            print(f"   取消标记结果: {'✅ 成功' if unmark_result else '❌ 失败'}")
            
            if unmark_result:
                # 7. 验证取消标记状态
                print("\n7️⃣ 验证取消标记后的状态...")
                final_status = get_photo_mark_status(test_photo_uuid)
                print(f"   最终状态: {final_status}")
                
                # 8. 获取最终摘要
                print("\n8️⃣ 获取最终已标记照片摘要...")
                final_summary = get_marked_photos_summary()
                print(f"   最终已标记照片数量: {final_summary['marked_count']}")
                
                # 验证结果
                print("\n🔍 验证测试结果...")
                if (initial_status == False and 
                    marked_status == True and 
                    final_status == False and
                    updated_summary['marked_count'] > initial_summary['marked_count'] and
                    final_summary['marked_count'] == initial_summary['marked_count']):
                    print("✅ 所有测试通过！照片标记删除功能工作正常。")
                    return True
                else:
                    print("❌ 测试失败！状态变化不符合预期。")
                    return False
            else:
                print("❌ 取消标记失败！")
                return False
        else:
            print("❌ 标记照片失败！")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_migration():
    """测试数据库迁移功能"""
    print("\n🔄 测试数据库迁移功能...")
    
    try:
        # 初始化数据库（这会自动运行迁移）
        Session = initialize_database()
        session = Session()
        
        # 检查marked_del列是否存在
        from sqlalchemy import text
        result = session.execute(text("PRAGMA table_info(photos)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'marked_del' in columns:
            print("✅ 数据库迁移成功！marked_del列已存在。")
            return True
        else:
            print("❌ 数据库迁移失败！marked_del列不存在。")
            return False
            
    except Exception as e:
        print(f"❌ 数据库迁移测试失败: {e}")
        return False
    finally:
        session.close()


def print_test_summary(workflow_result, migration_result):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("📋 集成测试摘要")
    print("="*60)
    print(f"数据库迁移测试: {'✅ 通过' if migration_result else '❌ 失败'}")
    print(f"照片标记工作流程测试: {'✅ 通过' if workflow_result else '❌ 失败'}")
    
    if workflow_result and migration_result:
        print("\n🎉 所有集成测试通过！照片标记删除功能已准备就绪。")
        print("\n📖 使用说明:")
        print("   1. 鼠标悬浮在照片上")
        print("   2. 按 'D' 键标记/取消标记照片")
        print("   3. 按 'H' 键切换显示已标记照片")
        print("   4. 已标记照片会自动添加到系统相册")
        print("   5. 默认情况下已标记照片会被隐藏")
    else:
        print("\n❌ 部分测试失败，请检查实现。")
    
    print("="*60)


async def main():
    """主函数"""
    print("🚀 启动照片标记删除功能集成测试")
    print("="*60)
    
    # 运行数据库迁移测试
    migration_result = await test_database_migration()
    
    # 运行工作流程测试
    workflow_result = await test_photo_marking_workflow()
    
    # 打印测试摘要
    print_test_summary(workflow_result, migration_result)
    
    return workflow_result and migration_result


if __name__ == "__main__":
    # 运行集成测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
