// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod models;
mod python_bridge;

use commands::*;
mod logger;
mod logging_models;
use logger::*;
use tauri::Manager;

fn main() {
    // Enhanced logging setup
    setup_development_logging();
    log::info!("🚀 Starting Tauri application...");

    // 记录环境信息
    log::info!("🔧 Cargo 版本: {}", env!("CARGO_PKG_VERSION"));
    log::info!(
        "🔧 构建模式: {}",
        if cfg!(debug_assertions) {
            "Debug"
        } else {
            "Release"
        }
    );

    // 记录当前工作目录
    if let Ok(current_dir) = std::env::current_dir() {
        log::info!("📁 当前工作目录: {:?}", current_dir);
    }

    // 记录可用的命令
    log::debug!("📋 注册的 Tauri 命令:");
    log::debug!("  - greet");
    log::debug!("  - open_devtools");
    log::debug!("  - get_library_info");
    log::debug!("  - collect_photos");
    log::debug!("  - generate_thumbnails");
    log::debug!("  - detect_temporal_groups");
    log::debug!("  - validate_library_path");
    log::debug!("  - get_default_photos_library");
    log::debug!("  - find_photos_libraries");
    log::debug!("  - collect_photos_from_library");
    log::debug!("  - collect_photos_by_date_range");
    log::debug!("  - get_thumbnail_dir");
    log::debug!("  - analyze_photos");
    log::debug!("  - analyze_photos_by_timeline");
    log::debug!("  - add_photos_to_timeline_incrementally");
    log::debug!("  - analyze_photos_optimized");
    log::debug!("  - process_photo_library");
    log::debug!("  - get_photo_thumbnail_details");

    log::info!("🔌 初始化 Tauri 插件...");
    let app_result = tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            open_devtools,
            get_library_info,
            collect_photos,
            generate_thumbnails,
            detect_temporal_groups,
            validate_library_path,
            get_default_photos_library,
            find_photos_libraries,
            collect_photos_from_library,
            collect_photos_by_date_range,
            collect_photos_by_date_added_range,
            collect_photos_timeline_by_added_date,
            collect_photos_with_smart_cache,
            collect_photos_with_smart_cache_by_date_range,
            clear_database_cache,
            get_thumbnail_dir,
            analyze_photos,
            analyze_photos_by_timeline,
            add_photos_to_timeline_incrementally,
            analyze_photos_optimized,
            process_photo_library,
            get_photo_thumbnail_details,
            mark_photo_for_deletion,
            unmark_photo_for_deletion,
            get_photo_mark_status,
            get_marked_photos_summary,
            initialize_photo_marking_system
        ])
        .setup(|app| {
            log::info!("🎯 Tauri 应用设置完成");
            log::info!("🌐 应用标识符: {}", app.config().identifier);

            // 在开发模式下启用开发者工具
            #[cfg(debug_assertions)]
            {
                let window = app.get_webview_window("main").unwrap();
                window.open_devtools();
                log::info!("🔧 开发者工具已启用");
            }

            Ok(())
        })
        .run(tauri::generate_context!());

    match app_result {
        Ok(_) => {
            log::info!("✅ Tauri 应用正常退出");
        }
        Err(e) => {
            log::error!("❌ Tauri 应用运行失败: {}", e);
            std::process::exit(1);
        }
    }
}
