use crate::logger::PerformanceTimer;
use crate::models::{LibraryInfo, PhotoInfo, SimilarityGroup, ThumbnailRequest};
use crate::python_bridge::PythonBridge;
use crate::{log_command_complete, log_command_error, log_command_start};
use serde::{Deserialize, Serialize};
use std::fs;
use std::time::Instant;
use tauri::command;

/// 确保输出目录存在
fn ensure_output_directory(output_dir: &str) -> Result<(), String> {
    if !std::path::Path::new(output_dir).exists() {
        fs::create_dir_all(output_dir)
            .map_err(|e| format!("无法创建输出目录 {}: {}", output_dir, e))?;
        log::info!("📁 已创建输出目录: {}", output_dir);
    }
    Ok(())
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LibraryInfoRequest {
    pub path: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CollectPhotosRequest {
    pub library_path: String,
    pub output_dir: String,
    pub days_back: i32,
    pub max_photos: i32,
    pub db_path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DetectGroupsRequest {
    pub photos: Vec<PhotoInfo>,
    pub interval_seconds: i32,
    pub similarity_threshold: f32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessLibraryRequest {
    pub library_path: String,
    pub output_dir: String,
    pub db_path: String,
    pub days_back: i32,
    pub max_photos: i32,
    pub thumbnail_size: String,
    pub thumbnail_quality: i32,
    pub time_threshold_seconds: i32,
    pub similarity_threshold: f32,
}

#[command]
pub async fn greet(name: String) -> Result<String, String> {
    let _timer = PerformanceTimer::new("greet");
    log::info!("💬 Greeting user: {}", name);
    Ok(format!("Hello, {}! You've been greeted from Rust!", name))
}

#[command]
pub async fn open_devtools(window: tauri::WebviewWindow) -> Result<(), String> {
    #[cfg(debug_assertions)]
    {
        window.open_devtools();
        log::info!("🔧 手动打开开发者工具");
    }
    Ok(())
}

#[command]
pub async fn get_library_info(request: LibraryInfoRequest) -> Result<LibraryInfo, String> {
    let start = Instant::now();
    log_command_start!("get_library_info", &request.path);

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("get_library_info", e);
        e.to_string()
    })?;

    let result = bridge.get_library_info(&request.path).await.map_err(|e| {
        log_command_error!("get_library_info", e);
        e.to_string()
    });

    match result {
        Ok(info) => {
            log_command_complete!("get_library_info", start.elapsed());
            log::info!(
                "📊 Library info: {} photos in {}",
                info.photo_count,
                request.path
            );
            Ok(info)
        }
        Err(e) => Err(e),
    }
}

#[command]
pub async fn validate_library_path(path: String) -> Result<bool, String> {
    use std::path::Path;
    Ok(Path::new(&path).exists())
}

#[command]
pub async fn get_default_photos_library() -> Result<Option<String>, String> {
    let start = Instant::now();
    log_command_start!(
        "get_default_photos_library",
        "checking default Photos Library"
    );

    // 获取用户主目录
    let home_dir = dirs::home_dir().ok_or_else(|| {
        let error = "无法获取用户主目录".to_string();
        log_command_error!("get_default_photos_library", error.clone());
        error
    })?;

    log::debug!("🏠 用户主目录: {:?}", home_dir);

    // 默认的 Photos Library 路径
    let default_library_path = home_dir
        .join("Pictures")
        .join("Photos Library.photoslibrary");

    log::debug!("🔍 检查默认路径: {:?}", default_library_path);

    if default_library_path.exists() && default_library_path.is_dir() {
        let path_str = default_library_path.to_string_lossy().to_string();
        log_command_complete!("get_default_photos_library", start.elapsed());
        log::info!("✅ 找到默认 Photos Library: {}", path_str);
        Ok(Some(path_str))
    } else {
        log_command_complete!("get_default_photos_library", start.elapsed());
        log::info!("❌ 默认 Photos Library 不存在: {:?}", default_library_path);

        // 检查 Pictures 目录是否存在
        let pictures_dir = home_dir.join("Pictures");
        if pictures_dir.exists() {
            log::debug!("📁 Pictures 目录存在，检查其内容");
            if let Ok(entries) = std::fs::read_dir(&pictures_dir) {
                let mut found_libraries = Vec::new();
                for entry in entries.flatten() {
                    let path = entry.path();
                    if let Some(name) = path.file_name() {
                        if let Some(name_str) = name.to_str() {
                            if name_str.ends_with(".photoslibrary") {
                                found_libraries.push(name_str.to_string());
                            }
                        }
                    }
                }
                if !found_libraries.is_empty() {
                    log::info!(
                        "📸 在 Pictures 目录中找到其他 Photos Library: {:?}",
                        found_libraries
                    );
                } else {
                    log::debug!("📁 Pictures 目录中没有找到任何 .photoslibrary 文件");
                }
            }
        } else {
            log::warn!("⚠️ Pictures 目录不存在: {:?}", pictures_dir);
        }

        Ok(None)
    }
}

#[command]
pub async fn find_photos_libraries(directory: String) -> Result<Vec<String>, String> {
    use std::fs;
    use std::path::Path;

    let dir_path = Path::new(&directory);
    if !dir_path.exists() || !dir_path.is_dir() {
        return Err("指定的路径不存在或不是目录".to_string());
    }

    let mut libraries = Vec::new();

    // 递归搜索 .photoslibrary 文件
    fn search_libraries(
        dir: &Path,
        libraries: &mut Vec<String>,
        max_depth: usize,
    ) -> Result<(), std::io::Error> {
        if max_depth == 0 {
            return Ok(());
        }

        let entries = fs::read_dir(dir)?;
        for entry in entries {
            let entry = entry?;
            let path = entry.path();

            if path.is_dir() {
                if let Some(name) = path.file_name() {
                    if let Some(name_str) = name.to_str() {
                        if name_str.ends_with(".photoslibrary") {
                            // 找到 Photos Library
                            if let Some(path_str) = path.to_str() {
                                libraries.push(path_str.to_string());
                            }
                        } else if !name_str.starts_with('.') {
                            // 继续搜索子目录（跳过隐藏目录）
                            let _ = search_libraries(&path, libraries, max_depth - 1);
                        }
                    }
                }
            }
        }
        Ok(())
    }

    // 搜索最多3层深度
    search_libraries(dir_path, &mut libraries, 3).map_err(|e| format!("搜索过程中出错: {}", e))?;

    log::info!(
        "在目录 {} 中找到 {} 个 Photos Library",
        directory,
        libraries.len()
    );
    for lib in &libraries {
        log::info!("  - {}", lib);
    }

    Ok(libraries)
}

#[command]
pub async fn collect_photos_from_library(
    library_path: String,
    days_back: i32,
    max_photos: i32,
    db_path: Option<String>,
) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos_from_library",
        format!(
            "library={}, days_back={}, max_photos={}, db_path={:?}",
            library_path, days_back, max_photos, db_path
        )
    );

    // 验证库路径
    log::debug!("🔍 验证库路径: {}", library_path);
    if !std::path::Path::new(&library_path).exists() {
        let error = format!("库路径不存在: {}", library_path);
        log_command_error!("collect_photos_from_library", error.clone());
        return Err(error);
    }

    log::debug!("🔧 创建 Python Bridge...");
    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos_from_library", e);
        e.to_string()
    })?;
    log::debug!("✅ Python Bridge 创建成功");

    let output_dir = "/tmp/photo_output".to_string();
    log::debug!("📁 输出目录: {}", output_dir);

    // 确保输出目录存在
    ensure_output_directory(&output_dir).map_err(|e| {
        log_command_error!("collect_photos_from_library", e.clone());
        e
    })?;

    log::debug!("🚀 开始调用 Python 收集照片...");
    let result = bridge
        .collect_photos(&library_path, &output_dir, days_back, max_photos, db_path)
        .await
        .map_err(|e| {
            log_command_error!("collect_photos_from_library", e);
            e.to_string()
        });

    match result {
        Ok(photos) => {
            log_command_complete!("collect_photos_from_library", start.elapsed());
            log::info!(
                "📸 Collected {} photos from library: {}",
                photos.len(),
                library_path
            );

            // 记录一些照片的详细信息用于调试
            if !photos.is_empty() {
                log::debug!("📋 前几张照片信息:");
                for (i, photo) in photos.iter().take(3).enumerate() {
                    log::debug!(
                        "  {}. UUID: {}, 原始路径: {}",
                        i + 1,
                        photo.uuid,
                        photo.original_path
                    );
                }
            }

            Ok(photos)
        }
        Err(e) => {
            log::error!("❌ 收集照片失败: {}", e);
            Err(e)
        }
    }
}

// 注意：已移除 collect_photos_from_directory 函数
// 本应用只支持 Apple Photos Library.photoslibrary

#[command]
pub async fn get_thumbnail_dir() -> Result<String, String> {
    let home_dir = dirs::home_dir().ok_or_else(|| "无法获取用户主目录".to_string())?;
    let thumbnail_dir = home_dir.join(".photo_thumbnails");
    std::fs::create_dir_all(&thumbnail_dir).map_err(|e| e.to_string())?;
    Ok(thumbnail_dir.to_string_lossy().to_string())
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ThumbnailDetails {
    pub photo_uuid: String,
    pub original_path: String,
    pub original_resolution: String,
    pub thumbnails: Vec<ThumbnailVariant>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ThumbnailVariant {
    pub path: String,
    pub resolution: String,
    pub size_bytes: u64,
    pub exists: bool,
    pub file_url: Option<String>,
}

#[command]
pub async fn get_photo_thumbnail_details(
    photo_uuid: String,
    library_path: Option<String>,
) -> Result<ThumbnailDetails, String> {
    let bridge = PythonBridge::new().map_err(|e| e.to_string())?;

    // Get thumbnail details from Python
    let details = bridge
        .get_photo_thumbnail_details(photo_uuid.clone(), library_path)
        .await
        .map_err(|e| e.to_string())?;

    Ok(details)
}

#[command]
pub async fn analyze_photos(
    photos: Vec<PhotoInfo>,
    db_path: Option<String>,
    time_threshold_seconds: Option<i32>,
    similarity_threshold: Option<f32>,
) -> Result<serde_json::Value, String> {
    let start = Instant::now();
    log_command_start!(
        "analyze_photos",
        format!(
            "photos={}, db_path={:?}, time_threshold={:?}, similarity_threshold={:?}",
            photos.len(),
            db_path,
            time_threshold_seconds,
            similarity_threshold
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("analyze_photos", e);
        e.to_string()
    })?;

    // 使用传入的参数，如果没有则使用默认值
    let time_threshold = time_threshold_seconds.unwrap_or(300);
    let similarity_threshold = similarity_threshold.unwrap_or(0.85);

    let groups = bridge
        .detect_temporal_groups(
            photos.clone(),
            time_threshold,
            similarity_threshold,
            db_path,
        )
        .await
        .map_err(|e| {
            log_command_error!("analyze_photos", e);
            e.to_string()
        })?;

    log_command_complete!("analyze_photos", start.elapsed());
    let duplicate_count: usize = groups.iter().map(|g| g.photos.len() - 1).sum();
    log::info!(
        "📈 Analysis summary: {} photos analyzed, {} similarity groups, {} potential duplicates",
        photos.len(),
        groups.len(),
        duplicate_count
    );

    Ok(serde_json::json!({
        "similarity_groups": groups,
        "total_photos": photos.len(),
        "duplicate_count": duplicate_count,
        "analysis_time_ms": start.elapsed().as_millis()
    }))
}

#[command]
pub async fn collect_photos_with_smart_cache(
    library_path: String,
    days_to_fetch: Option<i32>,
    max_photos: Option<i32>,
    db_path: Option<String>,
) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos_with_smart_cache",
        format!(
            "library={}, days_to_fetch={:?}, max_photos={:?}, db_path={:?}",
            library_path, days_to_fetch, max_photos, db_path
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos_with_smart_cache", e);
        e.to_string()
    })?;

    let photos = bridge
        .collect_photos_with_smart_cache(
            library_path,
            days_to_fetch.unwrap_or(5),
            max_photos.unwrap_or(0),
            db_path,
        )
        .await
        .map_err(|e| {
            log_command_error!("collect_photos_with_smart_cache", e);
            e.to_string()
        })?;

    log_command_complete!("collect_photos_with_smart_cache", start.elapsed());
    Ok(photos)
}

/// 清空数据库缓存
#[tauri::command]
pub async fn clear_database_cache(db_path: Option<String>) -> Result<String, String> {
    let start = std::time::Instant::now();
    log_command_start!("clear_database_cache", format!("db_path={:?}", db_path));

    let bridge =
        PythonBridge::new().map_err(|e| format!("Failed to create Python bridge: {}", e))?;

    match bridge.clear_database_cache(db_path).await {
        Ok(result) => {
            log_command_complete!("clear_database_cache", start.elapsed());
            Ok(result)
        }
        Err(e) => {
            log::error!("❌ 清空数据库缓存失败: {}", e);
            Err(format!("清空数据库缓存失败: {}", e))
        }
    }
}

#[command]
pub async fn collect_photos_with_smart_cache_by_date_range(
    library_path: String,
    start_date: i64, // Unix timestamp (seconds)
    end_date: i64,   // Unix timestamp (seconds)
    max_photos: Option<i32>,
    db_path: Option<String>,
) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos_with_smart_cache_by_date_range",
        format!(
            "library={}, start_date={}, end_date={}, max_photos={:?}, db_path={:?}",
            library_path, start_date, end_date, max_photos, db_path
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos_with_smart_cache_by_date_range", e);
        e.to_string()
    })?;

    let photos = bridge
        .collect_photos_with_smart_cache_by_date_range(
            library_path,
            start_date,
            end_date,
            max_photos.unwrap_or(0),
            db_path,
        )
        .await
        .map_err(|e| {
            log_command_error!("collect_photos_with_smart_cache_by_date_range", e);
            e.to_string()
        })?;

    log_command_complete!(
        "collect_photos_with_smart_cache_by_date_range",
        start.elapsed()
    );
    Ok(photos)
}

#[command]
pub async fn collect_photos_by_date_range(
    library_path: String,
    start_date: i64, // Unix timestamp (seconds)
    end_date: i64,   // Unix timestamp (seconds)
    max_photos: Option<i32>,
    db_path: Option<String>,
) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos_by_date_range",
        format!(
            "library={}, start_date={}, end_date={}, max_photos={:?}, db_path={:?}",
            library_path, start_date, end_date, max_photos, db_path
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos_by_date_range", e);
        e.to_string()
    })?;

    let photos = bridge
        .collect_photos_by_date_range(
            library_path,
            start_date,
            end_date,
            max_photos.unwrap_or(0),
            db_path,
        )
        .await
        .map_err(|e| {
            log_command_error!("collect_photos_by_date_range", e);
            e.to_string()
        })?;

    log_command_complete!("collect_photos_by_date_range", start.elapsed());
    Ok(photos)
}

#[command]
pub async fn collect_photos_by_date_added_range(
    library_path: String,
    start_date: i64, // Unix timestamp (seconds)
    end_date: i64,   // Unix timestamp (seconds)
    max_photos: Option<i32>,
    db_path: Option<String>,
) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos_by_date_added_range",
        format!(
            "library={}, start_date={}, end_date={}, max_photos={:?}, db_path={:?}",
            library_path, start_date, end_date, max_photos, db_path
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos_by_date_added_range", e);
        e.to_string()
    })?;

    let photos = bridge
        .collect_photos_by_date_added_range(
            library_path,
            start_date,
            end_date,
            max_photos.unwrap_or(0),
            db_path,
        )
        .await
        .map_err(|e| {
            log_command_error!("collect_photos_by_date_added_range", e);
            e.to_string()
        })?;

    log_command_complete!("collect_photos_by_date_added_range", start.elapsed());
    Ok(photos)
}

#[command]
pub async fn collect_photos_timeline_by_added_date(
    library_path: String,
    days_per_segment: Option<i32>,
    max_segments: Option<i32>,
    max_photos_per_segment: Option<i32>,
) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos_timeline_by_added_date",
        format!(
            "library={}, days_per_segment={:?}, max_segments={:?}, max_photos_per_segment={:?}",
            library_path, days_per_segment, max_segments, max_photos_per_segment
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos_timeline_by_added_date", e);
        e.to_string()
    })?;

    let photos = bridge
        .collect_photos_timeline_by_added_date(
            library_path.clone(),
            days_per_segment.unwrap_or(5),
            max_segments.unwrap_or(10),
            max_photos_per_segment.unwrap_or(50),
        )
        .await
        .map_err(|e| {
            log_command_error!("collect_photos_timeline_by_added_date", e);
            e.to_string()
        })?;

    log_command_complete!("collect_photos_timeline_by_added_date", start.elapsed());
    log::info!(
        "📊 Timeline collection complete: {} photos processed from {}",
        photos.len(),
        library_path
    );
    Ok(photos)
}

#[command]
pub async fn analyze_photos_by_timeline(
    photos: Vec<PhotoInfo>,
    time_threshold_seconds: Option<i32>,
    max_workers: Option<i32>,
    ahash_threshold: Option<f32>,
    phash_threshold: Option<f32>,
) -> Result<serde_json::Value, String> {
    let start = Instant::now();
    log_command_start!(
        "analyze_photos_by_timeline",
        format!(
            "photos={}, time_threshold_seconds={:?}, max_workers={:?}, ahash_threshold={:?}, phash_threshold={:?}",
            photos.len(),
            time_threshold_seconds,
            max_workers,
            ahash_threshold,
            phash_threshold
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("analyze_photos_by_timeline", e);
        e.to_string()
    })?;

    let result = bridge
        .analyze_photos_by_timeline(
            photos,
            time_threshold_seconds.unwrap_or(300),
            max_workers.unwrap_or(4),
            ahash_threshold.unwrap_or(0.1),
            phash_threshold.unwrap_or(0.1),
        )
        .await
        .map_err(|e| {
            log_command_error!("analyze_photos_by_timeline", e);
            e.to_string()
        })?;

    log_command_complete!("analyze_photos_by_timeline", start.elapsed());

    // 解析结果并记录统计信息
    if let Ok(analysis_result) = serde_json::from_str::<serde_json::Value>(&result) {
        if let Some(stats) = analysis_result.get("performance_stats") {
            if let (Some(timeline_groups), Some(total_photos)) = (
                stats.get("timeline_groups").and_then(|v| v.as_u64()),
                stats.get("total_photos").and_then(|v| v.as_u64()),
            ) {
                log::info!(
                    "⏰ 时间线分析统计: 总照片 {}, 时间线组 {}, 平均每组 {:.1} 张照片",
                    total_photos,
                    timeline_groups,
                    if timeline_groups > 0 {
                        total_photos as f64 / timeline_groups as f64
                    } else {
                        0.0
                    }
                );
            }
        }

        Ok(analysis_result)
    } else {
        Ok(serde_json::json!({"error": "Failed to parse timeline analysis result"}))
    }
}

#[command]
pub async fn add_photos_to_timeline_incrementally(
    analyzer_id: String,
    new_photos: Vec<PhotoInfo>,
) -> Result<serde_json::Value, String> {
    let start = Instant::now();
    log_command_start!(
        "add_photos_to_timeline_incrementally",
        format!(
            "analyzer_id={}, new_photos={}",
            analyzer_id,
            new_photos.len()
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("add_photos_to_timeline_incrementally", e);
        e.to_string()
    })?;

    let result = bridge
        .add_photos_to_timeline_incrementally(analyzer_id, new_photos)
        .await
        .map_err(|e| {
            log_command_error!("add_photos_to_timeline_incrementally", e);
            e.to_string()
        })?;

    log_command_complete!("add_photos_to_timeline_incrementally", start.elapsed());

    // 解析结果并记录统计信息
    if let Ok(analysis_result) = serde_json::from_str::<serde_json::Value>(&result) {
        if let Some(stats) = analysis_result.get("performance_stats") {
            if let (Some(affected_groups), Some(new_photos_count)) = (
                stats.get("affected_groups").and_then(|v| v.as_u64()),
                stats.get("new_photos_count").and_then(|v| v.as_u64()),
            ) {
                log::info!(
                    "🔄 增量分析统计: 新照片 {}, 影响组 {}",
                    new_photos_count,
                    affected_groups
                );
            }
        }

        Ok(analysis_result)
    } else {
        Ok(serde_json::json!({"error": "Failed to parse incremental analysis result"}))
    }
}

#[command]
pub async fn analyze_photos_optimized(
    photos: Vec<PhotoInfo>,
    db_path: Option<String>,
    batch_size: Option<i32>,
    max_workers: Option<i32>,
    ahash_threshold: Option<f32>,
    phash_threshold: Option<f32>,
) -> Result<serde_json::Value, String> {
    let start = Instant::now();
    log_command_start!("analyze_photos_optimized",
        format!("photos={}, db_path={:?}, batch_size={:?}, max_workers={:?}, ahash_threshold={:?}, phash_threshold={:?}",
            photos.len(), db_path, batch_size, max_workers, ahash_threshold, phash_threshold));

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("analyze_photos_optimized", e);
        e.to_string()
    })?;

    let result = bridge
        .analyze_photos_optimized(
            photos,
            db_path,
            batch_size.unwrap_or(50),
            max_workers.unwrap_or(4),
            ahash_threshold.unwrap_or(0.90),
            phash_threshold.unwrap_or(0.85),
        )
        .await
        .map_err(|e| {
            log_command_error!("analyze_photos_optimized", e);
            e.to_string()
        })?;

    log_command_complete!("analyze_photos_optimized", start.elapsed());

    // 解析结果并记录统计信息
    if let Ok(analysis_result) = serde_json::from_str::<serde_json::Value>(&result) {
        if let Some(stats) = analysis_result.get("efficiency_stats") {
            if let (Some(total), Some(ahash_calc), Some(phash_calc)) = (
                stats.get("total_photos").and_then(|v| v.as_u64()),
                stats.get("ahash_calculations").and_then(|v| v.as_u64()),
                stats.get("phash_calculations").and_then(|v| v.as_u64()),
            ) {
                log::info!(
                    "🚀 优化分析统计: 总照片 {}, AHash计算 {}, PHash计算 {} (效率提升: {}%)",
                    total,
                    ahash_calc,
                    phash_calc,
                    if total > 0 {
                        100 - ((ahash_calc + phash_calc) * 100 / (total * 2))
                    } else {
                        0
                    }
                );
            }
        }

        Ok(analysis_result)
    } else {
        Ok(serde_json::json!({"error": "Failed to parse analysis result"}))
    }
}

#[command]
pub async fn process_photo_library(
    request: ProcessLibraryRequest,
) -> Result<serde_json::Value, String> {
    let start = Instant::now();
    log_command_start!("process_photo_library", format!(
        "library_path={}, output_dir={}, db_path={}, days_back={}, max_photos={}, thumbnail_size={}, thumbnail_quality={}, time_threshold_seconds={}, similarity_threshold={}",
        request.library_path, request.output_dir, request.db_path, request.days_back, request.max_photos,
        request.thumbnail_size, request.thumbnail_quality, request.time_threshold_seconds, request.similarity_threshold
    ));

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("process_photo_library", e);
        e.to_string()
    })?;

    // Ensure output directory exists
    ensure_output_directory(&request.output_dir).map_err(|e| {
        log_command_error!("process_photo_library", e.clone());
        e
    })?;

    let result = bridge
        .process_library(
            &request.library_path,
            &request.output_dir,
            &request.db_path,
            request.days_back,
            request.max_photos,
            &request.thumbnail_size,
            request.thumbnail_quality,
            request.time_threshold_seconds,
            request.similarity_threshold,
        )
        .await
        .map_err(|e| {
            log_command_error!("process_photo_library", e);
            e.to_string()
        });

    match result {
        Ok(process_result) => {
            log_command_complete!("process_photo_library", start.elapsed());
            log::info!("✅ Library processing completed successfully");
            Ok(process_result)
        }
        Err(e) => Err(e),
    }
}

#[command]
pub async fn collect_photos(request: CollectPhotosRequest) -> Result<Vec<PhotoInfo>, String> {
    let start = Instant::now();
    log_command_start!(
        "collect_photos",
        format!(
            "library_path={}, output_dir={}, days_back={}, max_photos={}, db_path={:?}",
            request.library_path,
            request.output_dir,
            request.days_back,
            request.max_photos,
            request.db_path
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("collect_photos", e);
        e.to_string()
    })?;

    let result = bridge
        .collect_photos(
            &request.library_path,
            &request.output_dir,
            request.days_back,
            request.max_photos,
            request.db_path.clone(),
        )
        .await
        .map_err(|e| {
            log_command_error!("collect_photos", e);
            e.to_string()
        });

    match result {
        Ok(photos) => {
            log_command_complete!("collect_photos", start.elapsed());
            log::info!(
                "📊 Collection complete: {} photos processed in {}",
                photos.len(),
                request.library_path
            );
            Ok(photos)
        }
        Err(e) => Err(e),
    }
}

#[command]
pub async fn generate_thumbnails(
    photos: Vec<PhotoInfo>,
    config: ThumbnailRequest,
    db_path: Option<String>,
) -> Result<Vec<PhotoInfo>, String> {
    let bridge = PythonBridge::new().map_err(|e| e.to_string())?;

    bridge
        .generate_thumbnails(photos, &config, db_path)
        .await
        .map_err(|e| e.to_string())
}

#[command]
pub async fn detect_temporal_groups(
    request: DetectGroupsRequest,
    db_path: Option<String>,
) -> Result<Vec<SimilarityGroup>, String> {
    let start = Instant::now();
    log_command_start!(
        "detect_temporal_groups",
        format!(
            "photos={}, interval={}, threshold={}, db_path={:?}",
            request.photos.len(),
            request.interval_seconds,
            request.similarity_threshold,
            db_path
        )
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("detect_temporal_groups", e);
        e.to_string()
    })?;

    let result = bridge
        .detect_temporal_groups(
            request.photos,
            request.interval_seconds,
            request.similarity_threshold,
            db_path,
        )
        .await
        .map_err(|e| {
            log_command_error!("detect_temporal_groups", e);
            e.to_string()
        });

    match result {
        Ok(groups) => {
            log_command_complete!("detect_temporal_groups", start.elapsed());
            let total_photos_in_groups: usize = groups.iter().map(|g| g.photos.len()).sum();
            log::info!(
                "🔍 Analysis complete: found {} similarity groups with {} total photos",
                groups.len(),
                total_photos_in_groups
            );
            Ok(groups)
        }
        Err(e) => Err(e),
    }
}

/// 标记照片为删除状态
#[command]
pub async fn mark_photo_for_deletion(
    photo_uuid: String,
    library_path: Option<String>,
) -> Result<bool, String> {
    let start = Instant::now();
    log_command_start!(
        "mark_photo_for_deletion",
        format!("photo_uuid={}, library_path={:?}", photo_uuid, library_path)
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("mark_photo_for_deletion", e);
        e.to_string()
    })?;

    let result = bridge
        .mark_photo_for_deletion(photo_uuid.clone(), library_path)
        .await
        .map_err(|e| {
            log_command_error!("mark_photo_for_deletion", e);
            e.to_string()
        });

    match result {
        Ok(success) => {
            log_command_complete!("mark_photo_for_deletion", start.elapsed());
            if success {
                log::info!("✅ 照片 {} 已标记删除", photo_uuid);
            } else {
                log::warn!("⚠️ 照片 {} 标记删除失败", photo_uuid);
            }
            Ok(success)
        }
        Err(e) => Err(e),
    }
}

/// 取消照片的删除标记
#[command]
pub async fn unmark_photo_for_deletion(
    photo_uuid: String,
    library_path: Option<String>,
) -> Result<bool, String> {
    let start = Instant::now();
    log_command_start!(
        "unmark_photo_for_deletion",
        format!("photo_uuid={}, library_path={:?}", photo_uuid, library_path)
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("unmark_photo_for_deletion", e);
        e.to_string()
    })?;

    let result = bridge
        .unmark_photo_for_deletion(photo_uuid.clone(), library_path)
        .await
        .map_err(|e| {
            log_command_error!("unmark_photo_for_deletion", e);
            e.to_string()
        });

    match result {
        Ok(success) => {
            log_command_complete!("unmark_photo_for_deletion", start.elapsed());
            if success {
                log::info!("✅ 照片 {} 已取消删除标记", photo_uuid);
            } else {
                log::warn!("⚠️ 照片 {} 取消删除标记失败", photo_uuid);
            }
            Ok(success)
        }
        Err(e) => Err(e),
    }
}

/// 获取照片的标记状态
#[command]
pub async fn get_photo_mark_status(photo_uuid: String) -> Result<bool, String> {
    let start = Instant::now();
    log_command_start!(
        "get_photo_mark_status",
        format!("photo_uuid={}", photo_uuid)
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("get_photo_mark_status", e);
        e.to_string()
    })?;

    let result = bridge
        .get_photo_mark_status(photo_uuid.clone())
        .await
        .map_err(|e| {
            log_command_error!("get_photo_mark_status", e);
            e.to_string()
        });

    match result {
        Ok(marked) => {
            log_command_complete!("get_photo_mark_status", start.elapsed());
            log::debug!("📋 照片 {} 标记状态: {}", photo_uuid, marked);
            Ok(marked)
        }
        Err(e) => Err(e),
    }
}

/// 获取已标记照片的摘要信息
#[command]
pub async fn get_marked_photos_summary(
    library_path: Option<String>,
) -> Result<serde_json::Value, String> {
    let start = Instant::now();
    log_command_start!(
        "get_marked_photos_summary",
        format!("library_path={:?}", library_path)
    );

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("get_marked_photos_summary", e);
        e.to_string()
    })?;

    let result = bridge
        .get_marked_photos_summary(library_path)
        .await
        .map_err(|e| {
            log_command_error!("get_marked_photos_summary", e);
            e.to_string()
        });

    match result {
        Ok(summary) => {
            log_command_complete!("get_marked_photos_summary", start.elapsed());
            log::info!("📊 已标记照片摘要获取成功");
            Ok(summary)
        }
        Err(e) => Err(e),
    }
}

/// 初始化照片标记系统
#[command]
pub async fn initialize_photo_marking_system() -> Result<bool, String> {
    let start = Instant::now();
    log_command_start!("initialize_photo_marking_system", "");

    let bridge = PythonBridge::new().map_err(|e| {
        log_command_error!("initialize_photo_marking_system", e);
        e.to_string()
    })?;

    let result = bridge.initialize_photo_marking_system().await.map_err(|e| {
        log_command_error!("initialize_photo_marking_system", e);
        e.to_string()
    });

    match result {
        Ok(success) => {
            log_command_complete!("initialize_photo_marking_system", start.elapsed());
            if success {
                log::info!("✅ 照片标记系统初始化成功");
            } else {
                log::warn!("⚠️ 照片标记系统初始化部分失败");
            }
            Ok(success)
        }
        Err(e) => Err(e),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_greet_command() {
        let result = greet("World".to_string()).await;
        assert!(result.is_ok());
        assert_eq!(
            result.unwrap(),
            "Hello, World! You've been greeted from Rust!"
        );
    }

    #[tokio::test]
    async fn test_get_library_info_invalid() {
        let request = LibraryInfoRequest {
            path: "/invalid/path".to_string(),
        };
        let result = get_library_info(request).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_collect_photos_invalid() {
        let request = CollectPhotosRequest {
            library_path: "/invalid/path".to_string(),
            output_dir: "/tmp/output".to_string(),
            days_back: 30,
            max_photos: 100,
        };
        let result = collect_photos(request).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_detect_temporal_groups_empty() {
        let request = DetectGroupsRequest {
            photos: vec![],
            interval_seconds: 10,
            similarity_threshold: 0.85,
        };
        let result = detect_temporal_groups(request).await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_empty());
    }
}

#[cfg(test)]
mod mock_tests {
    use super::*;

    #[tokio::test]
    async fn test_thumbnail_request_serialization() {
        let request = ThumbnailRequest {
            library_path: "/test/path".to_string(),
            output_dir: "/tmp/output".to_string(),
            days_back: 30,
            max_photos: 100,
            thumbnail_size: "medium".to_string(),
            quality: 85,
        };

        let serialized = serde_json::to_string(&request).unwrap();
        let deserialized: ThumbnailRequest = serde_json::from_str(&serialized).unwrap();

        assert_eq!(request.library_path, deserialized.library_path);
        assert_eq!(request.thumbnail_size, deserialized.thumbnail_size);
        assert_eq!(request.quality, deserialized.quality);
    }

    #[tokio::test]
    async fn test_photo_info_serialization() {
        let photo = PhotoInfo {
            uuid: "test-uuid".to_string(),
            filename: "test.jpg".to_string(),
            original_path: "/test/test.jpg".to_string(),
            thumbnail_path: Some("/test/thumb.jpg".to_string()),
            date_taken: 1234567890,
            file_size: 1024,
            width: 1920,
            height: 1080,
            mime_type: "image/jpeg".to_string(),
            camera_model: Some("Test Camera".to_string()),
            latitude: Some(37.7749),
            longitude: Some(-122.4194),
            hash_values: None,
        };

        let serialized = serde_json::to_string(&photo).unwrap();
        let deserialized: PhotoInfo = serde_json::from_str(&serialized).unwrap();

        assert_eq!(photo.uuid, deserialized.uuid);
        assert_eq!(photo.filename, deserialized.filename);
        assert_eq!(photo.original_path, deserialized.original_path);
    }
}
