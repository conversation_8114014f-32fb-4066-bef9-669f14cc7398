import { useState, useEffect } from 'react';
import { invoke, convertFileSrc } from '@tauri-apps/api/core';
// import { open } from '@tauri-apps/plugin-dialog';
// import { readDir } from '@tauri-apps/plugin-fs';
import './App.css';

interface Photo {
    uuid: string;
    filename: string;
    original_path: string;
    date_taken: number;
    width: number;
    height: number;
    file_size: number;
    mime_type?: string; // 图片类型，如 image/jpeg, image/png 等
    thumbnail_path?: string;
    path_derivatives?: string[]; // 衍生图像路径列表，按文件大小排序（最大的在前）
    // 算法计算结果
    phash?: string;
    ahash?: string;
    dhash?: string;
    whash?: string;
    similarity_scores?: { [key: string]: number }; // 与其他照片的相似度分数
    marked_del?: boolean; // 标记为删除的照片
}

interface SimilarityGroup {
    group_id: string;
    photos: Photo[];
    similarity_score: number;
    photo_count: number;
}

function App() {
    const [photos, setPhotos] = useState<Photo[]>([]);
    const [similarityGroups, setSimilarityGroups] = useState<SimilarityGroup[]>(
        []
    );
    const [selectedPath, setSelectedPath] = useState<string>('');
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState<'photos' | 'duplicates'>(
        'photos'
    );
    const [defaultLibraryChecked, setDefaultLibraryChecked] = useState(false);
    const [photosPerRow, setPhotosPerRow] = useState<number>(8); // 默认每行8张图片
    const [duplicatesPerRow, setDuplicatesPerRow] = useState<number>(6); // 相似照片默认每行6张
    const [daysBack, setDaysBack] = useState<number>(5); // 默认查看最近5天的照片

    // 算法参数状态
    const [algorithmConfig, setAlgorithmConfig] = useState({
        timeThresholdSeconds: 300, // 5分钟
        similarityThreshold: 0.85, // 85%
        showAlgorithmDetails: false // 是否显示算法详情面板
    });

    // 分批加载相关状态
    const [batchLoadingEnabled, setBatchLoadingEnabled] = useState(true); // 默认启用分批加载
    const [minPhotosRequired, setMinPhotosRequired] = useState(50); // 最少需要的照片数量
    const [currentDate, setCurrentDate] = useState<Date>(() => {
        // 确保不会搜索未来日期，最多到今天
        const today = new Date();
        today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
        return today;
    }); // 当前加载到的日期
    const [photosByDate, setPhotosByDate] = useState<Map<string, Photo[]>>(
        new Map()
    ); // 按日期分组的照片
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMorePhotos, setHasMorePhotos] = useState(true);
    const [consecutiveEmptyDays, setConsecutiveEmptyDays] = useState(0); // 连续空日期计数

    // 优化分析相关状态
    const [useOptimizedAnalysis, setUseOptimizedAnalysis] = useState(true); // 默认启用优化分析
    const [optimizedConfig, setOptimizedConfig] = useState({
        batchSize: 50,
        maxWorkers: 4,
        ahashThreshold: 0.75, // 降低阈值以找到更多相似照片
        phashThreshold: 0.70  // 降低阈值以找到更多相似照片
    });
    const [timelineAnalyzerId, setTimelineAnalyzerId] = useState<string | null>(null); // 时间线分析器ID，用于增量分析

    // 照片标记删除相关状态
    const [showMarked, setShowMarked] = useState(false); // 是否显示已标记的照片
    const [hoveredPhoto, setHoveredPhoto] = useState<string | null>(null); // 当前悬浮的照片UUID
    const [markedPhotosCount, setMarkedPhotosCount] = useState(0); // 已标记照片数量

    // 控制每张照片的算法详情显示
    const [expandedPhotoDetails, setExpandedPhotoDetails] = useState<
        Set<string>
    >(new Set());

    const [progress, setProgress] = useState<{
        phase: string;
        current: number;
        total: number;
        message: string;
    } | null>(null);

    // 将mime_type转换为用户友好的图片类型显示
    const getImageTypeDisplay = (mimeType?: string): string => {
        if (!mimeType) return '未知';

        const typeMap: { [key: string]: string } = {
            'image/jpeg': 'JPEG',
            'image/jpg': 'JPEG',
            'image/png': 'PNG',
            'image/gif': 'GIF',
            'image/bmp': 'BMP',
            'image/tiff': 'TIFF',
            'image/tif': 'TIFF',
            'image/webp': 'WebP',
            'image/heic': 'HEIC',
            'image/heif': 'HEIF',
            'image/raw': 'RAW',
            'image/dng': 'DNG',
            'image/cr2': 'CR2',
            'image/nef': 'NEF',
            'image/arw': 'ARW',
            'image/orf': 'ORF',
            'image/rw2': 'RW2',
            'public.heic': 'HEIC',
            'public.heif': 'HEIF',
            'public.jpeg': 'JPEG',
            'public.png': 'PNG',
            'com.canon.cr2-raw-image': 'CR2',
            'com.nikon.nikon-electronic-format': 'NEF',
            'com.sony.arw-raw-image': 'ARW',
            'com.olympus.orf-raw-image': 'ORF',
            'com.panasonic.rw2-raw-image': 'RW2'
        };

        const normalizedType = mimeType.toLowerCase();
        return typeMap[normalizedType] || mimeType.replace('image/', '').toUpperCase();
    };

    // 获取原图路径，优先使用original_path，如果为空则使用path_derivatives[0]
    const getOriginalImagePath = (photo: Photo): string | null => {
        // 优先使用original_path
        if (photo.original_path && photo.original_path.trim() !== '') {
            return photo.original_path;
        }

        // 如果original_path为空，使用path_derivatives[0]（最大的衍生图像）
        if (photo.path_derivatives && photo.path_derivatives.length > 0) {
            return photo.path_derivatives[0];
        }

        // 如果都没有，返回null
        return null;
    };

    // 全屏显示状态
    const [fullscreenImage, setFullscreenImage] = useState<{
        src: string;
        filename: string;
    } | null>(null);

    // 点击图片显示原图
    const handleImageClick = (photo: Photo) => {
        const originalPath = getOriginalImagePath(photo);
        if (originalPath) {
            console.log(`🖼️ 显示原图: ${originalPath}`);
            // 使用Tauri的convertFileSrc转换路径并全屏显示
            const fileUrl = convertFileSrc(originalPath);
            setFullscreenImage({
                src: fileUrl,
                filename: photo.filename
            });
        } else {
            console.warn(`⚠️ 照片 ${photo.filename} 没有可用的原图路径`);
            // 如果没有原图路径，尝试显示缩略图
            if (photo.thumbnail_path) {
                console.log(`🖼️ 显示缩略图: ${photo.thumbnail_path}`);
                const thumbnailUrl = convertFileSrc(photo.thumbnail_path);
                setFullscreenImage({
                    src: thumbnailUrl,
                    filename: photo.filename
                });
            } else {
                alert('该照片没有可用的图片路径');
            }
        }
    };

    // 关闭全屏显示
    const closeFullscreen = () => {
        setFullscreenImage(null);
    };

    // 标记照片为删除
    const markPhoto = async (photoUuid: string) => {
        try {
            console.log(`🏷️ 标记照片删除: ${photoUuid}`);
            const success = await invoke<boolean>('mark_photo_for_deletion', {
                photoUuid,
                libraryPath: selectedPath
            });

            if (success) {
                // 乐观更新：立即更新本地状态
                setPhotos(prevPhotos =>
                    prevPhotos.map(photo =>
                        photo.uuid === photoUuid
                            ? { ...photo, marked_del: true }
                            : photo
                    )
                );

                // 更新按日期分组的照片
                setPhotosByDate(prevPhotosByDate => {
                    const newPhotosByDate = new Map(prevPhotosByDate);
                    for (const [date, photos] of newPhotosByDate) {
                        const updatedPhotos = photos.map(photo =>
                            photo.uuid === photoUuid
                                ? { ...photo, marked_del: true }
                                : photo
                        );
                        newPhotosByDate.set(date, updatedPhotos);
                    }
                    return newPhotosByDate;
                });

                // 更新已标记照片数量
                setMarkedPhotosCount(prev => prev + 1);

                console.log(`✅ 照片 ${photoUuid} 标记删除成功`);
            } else {
                console.error(`❌ 照片 ${photoUuid} 标记删除失败`);
                alert('标记照片失败，请重试');
            }
        } catch (error) {
            console.error(`❌ 标记照片删除时出错:`, error);
            alert('标记照片时出错: ' + error);
        }
    };

    // 取消照片标记
    const unmarkPhoto = async (photoUuid: string) => {
        try {
            console.log(`🏷️ 取消照片删除标记: ${photoUuid}`);
            const success = await invoke<boolean>('unmark_photo_for_deletion', {
                photoUuid,
                libraryPath: selectedPath
            });

            if (success) {
                // 乐观更新：立即更新本地状态
                setPhotos(prevPhotos =>
                    prevPhotos.map(photo =>
                        photo.uuid === photoUuid
                            ? { ...photo, marked_del: false }
                            : photo
                    )
                );

                // 更新按日期分组的照片
                setPhotosByDate(prevPhotosByDate => {
                    const newPhotosByDate = new Map(prevPhotosByDate);
                    for (const [date, photos] of newPhotosByDate) {
                        const updatedPhotos = photos.map(photo =>
                            photo.uuid === photoUuid
                                ? { ...photo, marked_del: false }
                                : photo
                        );
                        newPhotosByDate.set(date, updatedPhotos);
                    }
                    return newPhotosByDate;
                });

                // 更新已标记照片数量
                setMarkedPhotosCount(prev => Math.max(0, prev - 1));

                console.log(`✅ 照片 ${photoUuid} 取消标记成功`);
            } else {
                console.error(`❌ 照片 ${photoUuid} 取消标记失败`);
                alert('取消标记失败，请重试');
            }
        } catch (error) {
            console.error(`❌ 取消照片标记时出错:`, error);
            alert('取消标记时出错: ' + error);
        }
    };

    // 切换显示已标记照片
    const toggleShowMarked = () => {
        setShowMarked(prev => !prev);
        console.log(`🔄 切换显示已标记照片: ${!showMarked}`);
    };

    // 过滤照片列表（根据showMarked状态）
    const getFilteredPhotos = (photoList: Photo[]): Photo[] => {
        if (showMarked) {
            // 显示所有照片
            return photoList;
        } else {
            // 只显示未标记的照片
            return photoList.filter(photo => !photo.marked_del);
        }
    };

    // 处理键盘事件（ESC键关闭全屏，d键标记/取消标记，h键切换显示模式）
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // ESC键关闭全屏
            if (event.key === 'Escape' && fullscreenImage) {
                closeFullscreen();
                return;
            }

            // 如果在全屏模式下，不处理其他键盘事件
            if (fullscreenImage) {
                return;
            }

            // h键切换显示已标记照片
            if (event.key === 'h' || event.key === 'H') {
                event.preventDefault();
                toggleShowMarked();
                return;
            }

            // d键标记/取消标记照片（只在鼠标悬浮时生效）
            if ((event.key === 'd' || event.key === 'D') && hoveredPhoto) {
                event.preventDefault();

                // 找到悬浮的照片
                const photo = photos.find(p => p.uuid === hoveredPhoto) ||
                    Array.from(photosByDate.values()).flat().find(p => p.uuid === hoveredPhoto);

                if (photo) {
                    if (photo.marked_del) {
                        unmarkPhoto(photo.uuid);
                    } else {
                        markPhoto(photo.uuid);
                    }
                }
                return;
            }
        };

        // 始终监听键盘事件
        document.addEventListener('keydown', handleKeyDown);

        // 只在全屏模式下防止页面滚动
        if (fullscreenImage) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'auto';
        };
    }, [fullscreenImage, hoveredPhoto, photos, photosByDate, showMarked]);

    // 应用启动时初始化照片标记系统
    useEffect(() => {
        const initializeMarkingSystem = async () => {
            try {
                console.log('🚀 初始化照片标记系统...');
                const success = await invoke<boolean>('initialize_photo_marking_system');
                if (success) {
                    console.log('✅ 照片标记系统初始化成功');
                } else {
                    console.warn('⚠️ 照片标记系统初始化部分失败，但功能仍可使用');
                }
            } catch (error) {
                console.error('❌ 照片标记系统初始化失败:', error);
                // 不显示错误提示，因为这不影响核心功能
            }
        };

        initializeMarkingSystem();
    }, []); // 只在组件挂载时运行一次

    // 清空数据库缓存
    const handleClearDatabase = async () => {
        const confirmed = window.confirm(
            '确定要清空数据库缓存吗？\n\n这将删除所有缓存的照片信息和分析结果，但不会影响原始照片文件。\n清空后需要重新加载和分析照片。'
        );

        if (!confirmed) {
            return;
        }

        try {
            console.log('🗑️ 开始清空数据库缓存...');
            const result: string = await invoke('clear_database_cache');
            console.log('✅ 数据库缓存清空结果:', result);

            // 显示成功消息
            alert(result);

            // 清空当前的照片数据和状态
            setPhotos([]);
            setPhotosByDate(new Map());
            setSimilarityGroups([]);
            setTimelineAnalyzerId(null);

            console.log('🔄 已重置应用状态');
        } catch (error) {
            console.error('❌ 清空数据库缓存失败:', error);
            alert('清空数据库缓存失败: ' + error);
        }
    };

    // 按日期分组照片
    const groupPhotosByDate = (photosToGroup: Photo[]) => {
        const grouped = new Map<string, Photo[]>();
        photosToGroup.forEach(photo => {
            const date = new Date(photo.date_taken * 1000);
            const dateKey = date.toDateString();
            if (!grouped.has(dateKey)) {
                grouped.set(dateKey, []);
            }
            grouped.get(dateKey)!.push(photo);
        });

        // 按日期排序每组内的照片
        grouped.forEach((photos) => {
            photos.sort((a, b) => b.date_taken - a.date_taken);
        });

        setPhotosByDate(grouped);
        console.log(`📅 照片按日期分组完成，共 ${grouped.size} 个日期组`);
    };

    // 切换照片详情显示
    const togglePhotoDetails = (photoUuid: string) => {
        setExpandedPhotoDetails(prev => {
            const newSet = new Set(prev);
            if (newSet.has(photoUuid)) {
                newSet.delete(photoUuid);
            } else {
                newSet.add(photoUuid);
            }
            return newSet;
        });
    };

    // 在组件加载时自动检测默认 Photos Library
    useEffect(() => {
        // 防止React StrictMode导致的重复调用
        let isCancelled = false;

        const initializeLibrary = async () => {
            if (!isCancelled) {
                await checkDefaultPhotosLibrary();
            }
        };

        initializeLibrary();

        return () => {
            isCancelled = true;
        };
    }, []);

    // 监听时间范围变化，自动重新加载照片
    useEffect(() => {
        if (selectedPath && photos.length > 0) {
            console.log(`🔄 时间范围已更改为 ${daysBack} 天，重新加载照片...`);
            loadPhotos(selectedPath);
        }
    }, [daysBack]);

    // 响应式布局：根据屏幕宽度自动调整列数
    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            if (width <= 576) {
                setPhotosPerRow(Math.min(photosPerRow, 4)); // 小屏幕最多4列
                setDuplicatesPerRow(Math.min(duplicatesPerRow, 4));
            } else if (width <= 768) {
                setPhotosPerRow(Math.min(photosPerRow, 6)); // 中等屏幕最多6列
                setDuplicatesPerRow(Math.min(duplicatesPerRow, 5));
            }
            // 大屏幕保持用户设置的值
        };

        handleResize(); // 初始调用
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [photosPerRow, duplicatesPerRow]);

    // 改进的无限滚动监听 - 支持所有标签页
    useEffect(() => {
        if (!batchLoadingEnabled) return;

        const handleScroll = () => {
            if (isLoadingMore || !hasMorePhotos) {
                console.log('📜 滚动被阻止:', { isLoadingMore, hasMorePhotos });
                return;
            }

            const scrollTop =
                window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // 增加触发距离到500px，确保在不同屏幕尺寸下都能正常触发
            const triggerDistance = 500;
            const shouldTrigger = scrollTop + windowHeight >= documentHeight - triggerDistance;

            console.log('📜 滚动检测:', {
                scrollTop: Math.round(scrollTop),
                windowHeight,
                documentHeight,
                triggerDistance,
                shouldTrigger,
                distanceFromBottom: documentHeight - (scrollTop + windowHeight),
                activeTab
            });

            if (shouldTrigger) {
                console.log(`📜 触发无限滚动 - 当前标签页: ${activeTab}`);
                loadNextDay();
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [
        batchLoadingEnabled,
        isLoadingMore,
        hasMorePhotos,
        currentDate,
        selectedPath,
        consecutiveEmptyDays,
        activeTab
    ]);

    async function checkDefaultPhotosLibrary() {
        try {
            console.log('🏁 checkDefaultPhotosLibrary 开始');
            const defaultPath: string = await invoke('get_default_photos_library', {
                reason: 'checking default Photos Library'
            });

            if (defaultPath) {
                console.log('✅ 找到默认 Photos Library:', defaultPath);
                setSelectedPath(defaultPath);
                await loadPhotos(defaultPath);
            } else {
                console.log('⚠️ 未找到默认 Photos Library');
            }
        } catch (error) {
            console.error('❌ 检查默认 Photos Library 失败:', error);
        } finally {
            setDefaultLibraryChecked(true);
            console.log('🏁 checkDefaultPhotosLibrary 完成');
        }
    }

    async function loadPhotos(path: string) {
        if (!path) {
            console.error('❌ 路径为空，无法加载照片');
            return;
        }

        try {
            setIsLoading(true);
            setProgress({
                phase: 'loading',
                current: 0,
                total: 100,
                message: '正在加载照片...'
            });

            console.log('🚀 loadPhotos 开始，路径:', path);
            console.log('📊 当前配置:', {
                daysBack,
                batchLoadingEnabled,
                minPhotosRequired,
                useOptimizedAnalysis
            });

            if (batchLoadingEnabled) {
                // 分批加载模式
                console.log('📦 启用分批加载模式');
                setPhotos([]); // 清空现有照片
                setPhotosByDate(new Map()); // 清空按日期分组的照片
                setSimilarityGroups([]); // 清空相似组
                setTimelineAnalyzerId(null); // 重置时间线分析器ID

                // 确保currentDate设置为今天的最后一刻，这样搜索会从今天开始
                const today = new Date();
                today.setHours(23, 59, 59, 999);
                setCurrentDate(today);
                console.log(`📅 设置搜索起始日期为今天: ${today.toDateString()}`);

                setHasMorePhotos(true);
                setConsecutiveEmptyDays(0); // 重置连续空日期计数

                // 立即设置selectedPath，确保后续函数可以使用
                setSelectedPath(path);

                // 智能加载：确保至少获取指定数量的照片
                console.log(`📅 使用智能加载模式，目标至少${minPhotosRequired}张照片`);
                await loadPhotosUntilMinimum(minPhotosRequired, path);
            } else {
                // 传统模式：一次性加载所有照片（不限制数量）
                console.log('📦 使用传统加载模式');

                // 立即设置selectedPath，确保后续函数可以使用
                setSelectedPath(path);

                console.log('📞 调用 collect_photos_from_library，参数:', {
                    libraryPath: path,
                    daysBack: daysBack,
                    maxPhotos: 0
                });

                const loadedPhotos: Photo[] = await invoke(
                    'collect_photos_from_library',
                    {
                        libraryPath: path, // 使用驼峰命名
                        daysBack: daysBack,
                        maxPhotos: 0 // 0表示不限制数量
                    }
                );

                console.log(
                    '📸 collect_photos_from_library 返回照片数量:',
                    loadedPhotos.length
                );
                setPhotos(loadedPhotos);

                // 计算已标记照片数量
                const markedCount = loadedPhotos.filter(photo => photo.marked_del).length;
                setMarkedPhotosCount(markedCount);
                console.log(`📊 已标记照片数量: ${markedCount}`);

                // 分析相似性
                console.log('🔍 开始分析照片相似性...');
                await analyzePhotos(loadedPhotos);
            }
        } catch (error) {
            console.error('❌ 加载照片失败:', error);
            console.error('错误详情:', {
                message: String(error),
                type: typeof error,
                stack: error instanceof Error ? error.stack : 'No stack trace'
            });
            alert('加载照片时出错: ' + error);
        } finally {
            setIsLoading(false);
            setProgress(null);
            console.log('🏁 loadPhotos 完成');
        }
    }

    // 改进的加载下一批照片函数 - 智能累积到目标数量
    async function loadNextDay() {
        if (isLoadingMore || !hasMorePhotos) {
            console.log('📜 loadNextDay 被阻止:', { isLoadingMore, hasMorePhotos, consecutiveEmptyDays });
            return;
        }

        try {
            setIsLoadingMore(true);

            console.log(`🎯 开始累积加载，目标 ${minPhotosRequired} 张`);

            // 智能累积加载：从当前日期开始，按5天为单位累积加载
            let totalNewPhotos: Photo[] = [];
            let searchDate = new Date(currentDate);
            let consecutiveEmptyRanges = 0;
            const maxEmptyRanges = 3; // 最多允许3个连续的空范围
            const daysPerRange = daysBack; // 使用配置的天数范围（默认5天）

            while (totalNewPhotos.length < minPhotosRequired && consecutiveEmptyRanges < maxEmptyRanges) {
                // 计算下一个时间范围
                const rangeEnd = new Date(searchDate);
                const rangeStart = new Date(searchDate);
                rangeStart.setDate(rangeStart.getDate() - daysPerRange + 1);

                console.log(`🔍 查询范围 (${daysPerRange}天): ${rangeStart.toDateString()} 到 ${rangeEnd.toDateString()}`);
                console.log(`📊 进度: ${totalNewPhotos.length}/${minPhotosRequired} 张`);

                // 确保不会查询未来日期
                const now = new Date();
                if (rangeEnd > now) {
                    console.log(`⚠️ 检测到未来日期，调整查询范围`);
                    rangeEnd.setTime(now.getTime());
                    rangeStart.setDate(rangeEnd.getDate() - daysPerRange + 1);
                }

                const rangePhotos = await loadPhotosForDateRange(
                    rangeEnd,
                    daysPerRange,
                    undefined, // 不限制单次查询数量
                    selectedPath
                );

                if (rangePhotos.length > 0) {
                    // 过滤重复照片（基于UUID）
                    const existingUuids = new Set([...photos.map(p => p.uuid), ...totalNewPhotos.map(p => p.uuid)]);
                    const newPhotos = rangePhotos.filter(p => !existingUuids.has(p.uuid));

                    totalNewPhotos.push(...newPhotos);
                    consecutiveEmptyRanges = 0; // 重置空范围计数

                    console.log(`📸 新增 ${newPhotos.length} 张，累积 ${totalNewPhotos.length}/${minPhotosRequired} 张`);

                    // 只在数量很多时提示
                    if (rangePhotos.length > 200) {
                        console.log(`🔥 本范围照片较多: ${rangePhotos.length} 张`);
                    }
                } else {
                    consecutiveEmptyRanges++;
                    console.log(`📭 空范围，连续: ${consecutiveEmptyRanges}`);
                }

                // 向前推进搜索日期到下一个范围
                searchDate.setDate(searchDate.getDate() - daysPerRange);

                // 更新当前日期为最新搜索的结束日期
                setCurrentDate(new Date(rangeEnd));
            }

            // 处理结果
            if (totalNewPhotos.length > 0) {
                console.log(`✅ 累积加载完成: ${totalNewPhotos.length} 张新照片 ${totalNewPhotos.length >= minPhotosRequired ? '✅' : '⚠️'}`);

                // 更新照片列表
                const updatedPhotos = [...photos, ...totalNewPhotos];
                setPhotos(updatedPhotos);

                // 按日期分组更新
                groupPhotosByDate(updatedPhotos);

                // 更新已标记照片数量
                const markedCount = updatedPhotos.filter(photo => photo.marked_del).length;
                setMarkedPhotosCount(markedCount);

                // 重置连续空日期计数
                setConsecutiveEmptyDays(0);

                console.log(`📊 总照片数: ${updatedPhotos.length} 张，已标记: ${markedCount} 张`);

                // 如果当前在相似照片标签页，使用增量分析更新相似组
                if (activeTab === 'duplicates') {
                    console.log('🔍 检测到相似照片标签页，开始增量分析新照片...');
                    try {
                        if (timelineAnalyzerId && totalNewPhotos.length > 0) {
                            // 使用增量分析
                            console.log(`🔄 使用增量分析，分析器ID: ${timelineAnalyzerId}, 新照片数: ${totalNewPhotos.length}`);
                            const incrementalResult: any = await invoke('add_photos_to_timeline_incrementally', {
                                analyzerId: timelineAnalyzerId,
                                newPhotos: totalNewPhotos
                            });

                            console.log('🔍 增量分析结果:', incrementalResult);

                            // 更新相似组
                            if (incrementalResult && incrementalResult.similarity_groups) {
                                setSimilarityGroups(incrementalResult.similarity_groups);
                                console.log(`✅ 增量分析完成，更新了 ${incrementalResult.similarity_groups.length} 个相似组`);
                            }
                        } else {
                            // 如果没有分析器ID或者是第一次分析，进行完整分析
                            console.log('🔍 进行完整相似组分析...');
                            await analyzePhotos(updatedPhotos);
                        }
                    } catch (error) {
                        console.error('❌ 相似组分析失败:', error);
                        // 如果增量分析失败，回退到完整分析
                        try {
                            console.log('🔄 增量分析失败，回退到完整分析...');
                            await analyzePhotos(updatedPhotos);
                        } catch (fallbackError) {
                            console.error('❌ 完整分析也失败:', fallbackError);
                        }
                    }
                }
            } else {
                // 增加连续空日期计数
                const newConsecutiveEmptyDays = consecutiveEmptyDays + consecutiveEmptyRanges;
                setConsecutiveEmptyDays(newConsecutiveEmptyDays);
                console.log(`📭 本次加载没有获得新照片，连续空范围: ${newConsecutiveEmptyDays}`);

                // 如果连续多个范围都没有照片，停止加载
                if (newConsecutiveEmptyDays >= 7) {
                    console.log('🛑 连续多个范围没有照片，停止无限流加载');
                    setHasMorePhotos(false);
                }
            }

        } catch (error) {
            console.error('❌ loadNextDay 失败:', error);
        } finally {
            console.log('🔄 loadNextDay 完成，设置 isLoadingMore = false');
            setIsLoadingMore(false);
        }
    }

    // 智能加载：持续加载照片直到达到最小数量
    async function loadPhotosUntilMinimum(
        minPhotos: number,
        libraryPath: string
    ) {
        console.log(`🎯 开始智能加载，目标至少 ${minPhotos} 张照片`);

        let totalPhotos: Photo[] = [];
        let currentSearchDate = new Date();
        let daysSearched = 0;
        const maxDaysToSearch = 365; // 最多搜索一年
        let initialDaysBack = daysBack; // 初始搜索天数
        try {
            while (totalPhotos.length < minPhotos && daysSearched < maxDaysToSearch) {
                console.log(`🔍 第 ${Math.floor(daysSearched / initialDaysBack) + 1} 轮搜索，已获得 ${totalPhotos.length} 张照片`);

                console.log(`📊 本轮搜索参数: 日期范围=${initialDaysBack}天, 不限制照片数量`);
                console.log(`📅 搜索日期范围: ${new Date(currentSearchDate.getTime() - (initialDaysBack - 1) * 24 * 60 * 60 * 1000).toDateString()} 到 ${currentSearchDate.toDateString()}`);

                // 每次搜索指定天数的范围，不限制照片数量以了解实际分布
                const batchPhotos = await loadPhotosForDateRange(
                    currentSearchDate,
                    initialDaysBack,
                    undefined, // 不限制数量
                    libraryPath
                );

                if (batchPhotos.length > 0) {
                    // 过滤重复照片（基于UUID）
                    const existingUuids = new Set(totalPhotos.map(p => p.uuid));
                    const newPhotos = batchPhotos.filter(p => !existingUuids.has(p.uuid));

                    totalPhotos.push(...newPhotos);
                    console.log(`📸 本轮新增 ${newPhotos.length} 张照片（查询到${batchPhotos.length}张），总计 ${totalPhotos.length} 张`);

                    // 分析照片数量分布
                    if (batchPhotos.length > 100) {
                        console.log(`⚠️ 注意：本轮查询到 ${batchPhotos.length} 张照片，数量较多`);
                        console.log(`📊 可能原因：该${initialDaysBack}天时间段内照片密度较高`);
                    }

                    // 如果这是第一批照片，更新currentDate为搜索的起始日期（而不是照片的日期）
                    // 这样可以确保UI显示从今天开始，而不是跳到照片的实际日期
                    if (totalPhotos.length === newPhotos.length) {
                        // 第一批照片，保持currentDate为搜索起始日期
                        setCurrentDate(new Date(currentSearchDate));
                        console.log(`📅 设置显示起始日期为: ${currentSearchDate.toDateString()}`);
                    }
                } else {
                    console.log(`📭 ${currentSearchDate.toDateString()} 开始的 ${initialDaysBack} 天内没有照片`);
                }

                // 向前推进搜索日期
                currentSearchDate.setDate(currentSearchDate.getDate() - initialDaysBack);
                daysSearched += initialDaysBack;

                // 如果照片数量仍然不足，但已经搜索了很多天，可以考虑增加每次搜索的天数
                if (totalPhotos.length < minPhotos / 2 && daysSearched > 30) {
                    initialDaysBack = Math.min(initialDaysBack * 2, 30); // 最多30天一批
                    console.log(`📈 增加搜索范围到 ${initialDaysBack} 天/批`);
                }
            }

            if (totalPhotos.length >= minPhotos) {
                console.log(`✅ 智能加载完成！获得 ${totalPhotos.length} 张照片（目标 ${minPhotos} 张）`);
                console.log(`📊 搜索了 ${daysSearched} 天，平均每天 ${(totalPhotos.length / daysSearched).toFixed(2)} 张照片`);
            } else {
                console.log(`⚠️ 智能加载结束，仅获得 ${totalPhotos.length} 张照片（目标 ${minPhotos} 张）`);
                console.log(`📊 已搜索 ${daysSearched} 天，可能照片库中照片数量有限`);
                setHasMorePhotos(false);
            }

            // 设置最终的照片列表
            setPhotos(totalPhotos);

            // 按日期分组照片并更新currentDate为实际的最新照片日期
            if (totalPhotos.length > 0) {
                // 按日期分组
                groupPhotosByDate(totalPhotos);

                // 找到最新的照片日期，更新currentDate
                const latestPhoto = totalPhotos.reduce((latest, photo) =>
                    photo.date_taken > latest.date_taken ? photo : latest
                );

                // 正确处理日期格式
                let latestPhotoDate: Date;
                if (typeof latestPhoto.date_taken === 'number') {
                    // 判断是秒还是毫秒时间戳
                    if (latestPhoto.date_taken > 1000000000000) {
                        // 毫秒时间戳
                        latestPhotoDate = new Date(latestPhoto.date_taken);
                    } else {
                        // 秒时间戳
                        latestPhotoDate = new Date(latestPhoto.date_taken * 1000);
                    }
                } else {
                    // 字符串格式
                    latestPhotoDate = new Date(latestPhoto.date_taken);
                }

                // 确保不会设置未来日期
                const now = new Date();
                if (latestPhotoDate > now) {
                    console.log(`⚠️ 检测到未来日期 ${latestPhotoDate.toDateString()}，使用当前日期`);
                    latestPhotoDate = now;
                }

                setCurrentDate(latestPhotoDate);
                console.log(`📅 更新显示日期为最新照片日期: ${latestPhotoDate.toDateString()}`);

                console.log('🔍 开始分析照片相似性...');
                await analyzePhotos(totalPhotos);
            }

        } catch (error) {
            console.error('❌ 智能加载失败:', error);
            throw error;
        }
    }

    // 加载指定天数范围的照片
    async function loadPhotosForDateRange(
        startDate: Date,
        daysCount: number,
        maxPhotos?: number,
        libraryPath?: string
    ) {
        try {
            // 不在这里设置isLoadingMore，由调用方控制

            // 计算开始和结束时间戳
            const startOfRange = new Date(startDate);
            startOfRange.setHours(0, 0, 0, 0);

            const endOfRange = new Date(startDate);
            endOfRange.setDate(endOfRange.getDate() - daysCount + 1); // 向前推daysCount天，但包含起始日
            endOfRange.setHours(0, 0, 0, 0); // 设置为最早日期的开始

            const startTimestamp = Math.floor(endOfRange.getTime() / 1000); // 注意：这里是较早的日期
            const endTimestamp = Math.floor(startOfRange.getTime() / 1000); // 这里是较晚的日期

            console.log(`🔍 查询 ${daysCount}天范围: ${endOfRange.toDateString()} 到 ${startOfRange.toDateString()}`);

            const pathToUse = libraryPath || selectedPath;

            if (!pathToUse) {
                console.error('❌ 照片库路径为空，无法加载照片');
                return [];
            }

            // 使用智能缓存版本的日期范围查询
            const rangePhotos: Photo[] = await invoke(
                'collect_photos_with_smart_cache_by_date_range',
                {
                    libraryPath: pathToUse,
                    startDate: startTimestamp,
                    endDate: endTimestamp,
                    maxPhotos: maxPhotos || null
                }
            );

            console.log(`✅ 查询完成，返回 ${rangePhotos.length} 张照片`);

            // 分析照片数量
            if (rangePhotos.length > 500) {
                console.log(`🔥 注意：本次查询返回了大量照片 (${rangePhotos.length} 张)`);
                console.log(`📊 可能原因：该${daysCount}天时间段内照片密度很高`);
                console.log(`📈 平均每天: ${(rangePhotos.length / daysCount).toFixed(1)} 张照片`);
            } else if (rangePhotos.length > 100) {
                console.log(`📸 本次查询返回中等数量照片 (${rangePhotos.length} 张)`);
            }

            if (rangePhotos.length === 0) {
                console.log(`📭 ${daysCount}天范围内没有找到照片`);
                return [];
            }

            console.log(
                `${daysCount}天范围内找到 ${rangePhotos.length} 张照片`
            );

            // 调试：检查缩略图路径
            const withThumbnails = rangePhotos.filter(p => p.thumbnail_path);
            const withoutThumbnails = rangePhotos.filter(
                p => !p.thumbnail_path
            );
            console.log(
                `📸 ${daysCount}天范围 - 有缩略图: ${withThumbnails.length}, 无缩略图: ${withoutThumbnails.length}`
            );

            if (withoutThumbnails.length > 0) {
                console.log('⚠️ 部分照片没有缩略图，可能需要生成');
                console.log(
                    '📝 无缩略图的照片示例:',
                    withoutThumbnails.slice(0, 3).map(p => p.filename)
                );
            }

            return rangePhotos;
        } catch (error) {
            console.error(`❌ 加载 ${daysCount}天范围的照片失败:`, error);
            return [];
        }
    }

    // 移除未使用的 loadPhotosForDate 函数，现在使用智能累积加载

    async function analyzePhotos(photosToAnalyze?: Photo[]) {
        const targetPhotos = photosToAnalyze || photos;
        if (targetPhotos.length === 0) {
            return;
        }

        try {
            setIsLoading(true);
            setProgress({
                phase: 'analyzing',
                current: 0,
                total: targetPhotos.length,
                message: '正在分析照片相似性...'
            });

            console.log('🔍 开始分析照片相似性，照片数量:', targetPhotos.length);
            console.log('📊 分析配置:', {
                useOptimizedAnalysis,
                optimizedConfig,
                algorithmConfig
            });

            let result: any;
            if (useOptimizedAnalysis) {
                console.log('🚀 使用优化分析器');
                result = await invoke('analyze_photos_by_timeline', {
                    photos: targetPhotos,
                    config: {
                        timeThresholdSeconds: algorithmConfig.timeThresholdSeconds,
                        similarityThreshold: algorithmConfig.similarityThreshold,
                        batchSize: optimizedConfig.batchSize,
                        maxWorkers: optimizedConfig.maxWorkers,
                        ahashThreshold: optimizedConfig.ahashThreshold,
                        phashThreshold: optimizedConfig.phashThreshold
                    }
                });
            } else {
                console.log('🔄 使用传统分析器');
                result = await invoke('analyze_photos', {
                    photos: targetPhotos,
                    timeThresholdSeconds: algorithmConfig.timeThresholdSeconds,
                    similarityThreshold: algorithmConfig.similarityThreshold
                });
            }

            console.log('🔍 完整分析结果:', result);
            console.log('🔍 相似组数量:', result.similarity_groups?.length || 0);
            console.log('🔍 所有照片数量:', result.all_photos?.length || 0);

            // 如果使用时间线分析器，保存分析器ID以便后续增量分析
            if (useOptimizedAnalysis && result.analyzer_id) {
                setTimelineAnalyzerId(result.analyzer_id);
                console.log(`💾 保存时间线分析器ID: ${result.analyzer_id}`);
            }

            if (result.similarity_groups) {
                setSimilarityGroups(result.similarity_groups);
                console.log(`✅ 找到 ${result.similarity_groups.length} 个相似组`);
            } else {
                console.log('⚠️ 没有收到similarity_groups数据');
                setSimilarityGroups([]);
            }

            if (result.all_photos) {
                console.log(`📸 分析返回了 ${result.all_photos.length} 张照片`);

                // 在分批加载模式下，不要直接覆盖photos，而是更新photosByDate
                if (batchLoadingEnabled) {
                    console.log('📦 分批加载模式：更新按日期分组的照片数据');
                    // 重新按日期分组分析后的照片
                    groupPhotosByDate(result.all_photos);
                } else {
                    console.log('📦 传统模式：直接更新照片列表');
                    setPhotos(result.all_photos);
                    // 重新按日期分组
                    groupPhotosByDate(result.all_photos);
                }
            } else {
                console.log('⚠️ 没有收到all_photos数据');
            }

        } catch (error) {
            console.error('❌ 分析照片失败:', error);
            alert('分析照片时出错: ' + error);
        } finally {
            setIsLoading(false);
            setProgress(null);
        }
    }

    return (
        <div className='app'>
            <header className='app-header'>
                <div className='path-selection'>
                    {!selectedPath && !isLoading && defaultLibraryChecked && (
                        <div className='header-actions'>
                            <p className='no-default-library'>
                                未找到默认 Photos Library
                            </p>
                        </div>
                    )}


                </div>
            </header>

            <div className='tab-navigation'>
                <button
                    className={activeTab === 'photos' ? 'active' : ''}
                    onClick={() => setActiveTab('photos')}
                >
                    所有照片 ({photos.length})
                </button>
                <button
                    className={activeTab === 'duplicates' ? 'active' : ''}
                    onClick={async () => {
                        setActiveTab('duplicates');
                        // 如果切换到相似照片标签页，且有照片但没有相似组，自动分析
                        if (photos.length > 0 && similarityGroups.length === 0) {
                            console.log('🔍 切换到相似照片标签页，自动开始分析...');
                            try {
                                await analyzePhotos(photos);
                            } catch (error) {
                                console.error('❌ 自动分析失败:', error);
                            }
                        }
                    }}
                >
                    相似照片 ({similarityGroups.length})
                </button>
                <button
                    className={
                        algorithmConfig.showAlgorithmDetails ? 'active' : ''
                    }
                    onClick={() =>
                        setAlgorithmConfig(prev => ({
                            ...prev,
                            showAlgorithmDetails: !prev.showAlgorithmDetails
                        }))
                    }
                >
                    算法参数
                </button>
                <button
                    className={`show-marked-button ${showMarked ? 'active' : ''}`}
                    onClick={toggleShowMarked}
                    title="快捷键: H"
                >
                    {showMarked ? '隐藏已标记' : '显示已标记'} ({markedPhotosCount})
                </button>
            </div>

            {/* 算法参数控制面板 */}
            {algorithmConfig.showAlgorithmDetails && (
                <div className='algorithm-config-panel'>
                    <h3>算法参数配置</h3>

                    <div className='config-section'>
                        <h4>时间阈值</h4>
                        <div className='config-item'>
                            <label>
                                时间阈值 (秒):
                                <input
                                    type='number'
                                    min='60'
                                    max='3600'
                                    value={algorithmConfig.timeThresholdSeconds}
                                    onChange={e =>
                                        setAlgorithmConfig(prev => ({
                                            ...prev,
                                            timeThresholdSeconds: Number(e.target.value)
                                        }))
                                    }
                                />
                                <span className='config-description'>
                                    秒 (当前: {Math.floor(algorithmConfig.timeThresholdSeconds / 60)} 分钟)
                                </span>
                            </label>
                        </div>
                    </div>



                    <div className='config-section'>
                        <h4>加载模式</h4>
                        <div className='config-item'>
                            <label className='checkbox-label'>
                                <input
                                    type='checkbox'
                                    checked={batchLoadingEnabled}
                                    onChange={e =>
                                        setBatchLoadingEnabled(e.target.checked)
                                    }
                                />
                                启用分批加载模式 (推荐)
                                <span className='algorithm-description'>
                                    (按天分批加载，支持无限滚动，智能获取足够照片)
                                </span>
                            </label>
                        </div>

                        {batchLoadingEnabled && (
                            <div className='config-item'>
                                <label>
                                    最少照片数量:
                                    <input
                                        type='number'
                                        min='20'
                                        max='200'
                                        value={minPhotosRequired}
                                        onChange={e =>
                                            setMinPhotosRequired(Number(e.target.value))
                                        }
                                    />
                                    <span className='config-description'>
                                        张
                                    </span>
                                    <span className='config-help'>
                                        (自动加载直到达到此数量)
                                    </span>
                                </label>
                            </div>
                        )}
                    </div>

                    <div className='config-section'>
                        <h4>相似度分析算法</h4>
                        <div className='config-item'>
                            <label className='checkbox-label'>
                                <input
                                    type='checkbox'
                                    checked={useOptimizedAnalysis}
                                    onChange={e =>
                                        setUseOptimizedAnalysis(e.target.checked)
                                    }
                                />
                                启用优化分析器 (推荐)
                                <span className='algorithm-description'>
                                    (使用时间线分组和多阶段hash算法，性能更好)
                                </span>
                            </label>
                        </div>

                        {/* 通用相似度阈值 */}
                        <div className='config-item'>
                            <label>
                                最终相似度阈值:
                                <input
                                    type='range'
                                    min='0.5'
                                    max='0.99'
                                    step='0.01'
                                    value={algorithmConfig.similarityThreshold}
                                    onChange={e =>
                                        setAlgorithmConfig(prev => ({
                                            ...prev,
                                            similarityThreshold: Number(e.target.value)
                                        }))
                                    }
                                />
                                <span className='config-description'>
                                    {(algorithmConfig.similarityThreshold * 100).toFixed(0)}%
                                </span>
                                <span className='config-help'>
                                    (最终判定为相似的阈值)
                                </span>
                            </label>
                        </div>

                        {useOptimizedAnalysis ? (
                            <>
                                <div className='algorithm-explanation'>
                                    <p><strong>优化算法工作流程：</strong></p>
                                    <p>1. AHash快速筛选 → 2. PHash精确验证 → 3. 最终相似度判定</p>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        批处理大小:
                                        <input
                                            type='number'
                                            min='10'
                                            max='100'
                                            value={optimizedConfig.batchSize}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    batchSize: Number(e.target.value)
                                                }))
                                            }
                                        />
                                        <span className='config-description'>
                                            张/批
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        AHash阈值 (第一阶段):
                                        <input
                                            type='range'
                                            min='0.5'
                                            max='0.9'
                                            step='0.05'
                                            value={optimizedConfig.ahashThreshold}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    ahashThreshold: Number(e.target.value)
                                                }))
                                            }
                                        />
                                        <span className='config-description'>
                                            {(optimizedConfig.ahashThreshold * 100).toFixed(0)}%
                                        </span>
                                        <span className='config-help'>
                                            (快速筛选候选照片，建议低于最终阈值)
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        PHash阈值 (第二阶段):
                                        <input
                                            type='range'
                                            min='0.5'
                                            max='0.9'
                                            step='0.05'
                                            value={optimizedConfig.phashThreshold}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    phashThreshold: Number(e.target.value)
                                                }))
                                            }
                                        />
                                        <span className='config-description'>
                                            {(optimizedConfig.phashThreshold * 100).toFixed(0)}%
                                        </span>
                                        <span className='config-help'>
                                            (精确验证，建议低于最终阈值)
                                        </span>
                                    </label>
                                </div>

                                {/* 阈值合理性检查 */}
                                {(optimizedConfig.ahashThreshold >= algorithmConfig.similarityThreshold ||
                                    optimizedConfig.phashThreshold >= algorithmConfig.similarityThreshold) && (
                                        <div className='threshold-warning'>
                                            ⚠️ 建议AHash和PHash阈值低于最终相似度阈值，以确保多阶段筛选的有效性
                                        </div>
                                    )}
                            </>
                        ) : (
                            <div className='algorithm-explanation'>
                                <p><strong>传统算法：</strong>直接使用最终相似度阈值进行判定</p>
                            </div>
                        )}
                    </div>

                    <div className='config-section'>
                        <h4>时间范围</h4>
                        <div className='config-item'>
                            <label>
                                查看最近:
                                <input
                                    type='number'
                                    min='1'
                                    max='30'
                                    value={daysBack}
                                    onChange={e => setDaysBack(Number(e.target.value))}
                                />
                                <span className='config-description'>天的照片</span>
                            </label>
                        </div>
                    </div>

                    <div className='config-section'>
                        <h4>数据库管理</h4>
                        <div className='config-item'>
                            <button
                                className='clear-database-btn'
                                onClick={handleClearDatabase}
                                disabled={isLoading}
                                title='清空数据库缓存，释放存储空间'
                            >
                                🗑️ 清空数据库缓存
                            </button>
                            <span className='config-description'>
                                清空photo_manager.db，释放存储空间
                            </span>
                        </div>
                    </div>
                </div>
            )}

            <main className='app-main'>
                {isLoading && (
                    <div className='loading'>
                        <div className='spinner'></div>
                        <p>正在处理照片...</p>
                        {progress && (
                            <div className='progress-info'>
                                <p>{progress.message}</p>
                                <div className='progress-bar-container'>
                                    <div
                                        className='progress-bar'
                                        style={{
                                            width: `${(progress.current /
                                                progress.total) *
                                                100
                                                }%`
                                        }}
                                    ></div>
                                </div>
                                <p>
                                    {progress.current} / {progress.total}
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'photos' && photos.length > 0 && (
                    <div className='photo-controls'>
                        <label className='photos-per-row-control'>
                            每行显示图片数量:
                            <input
                                type='range'
                                min='6'
                                max='10'
                                value={photosPerRow}
                                onChange={e =>
                                    setPhotosPerRow(Number(e.target.value))
                                }
                                className='photos-per-row-slider'
                            />
                            <span className='photos-per-row-value'>
                                {photosPerRow}
                            </span>
                        </label>
                    </div>
                )}

                {activeTab === 'photos' && (
                    <div className='photos-container'>
                        {batchLoadingEnabled ? (
                            // 分批加载模式：按日期分组显示
                            <div className='photos-by-date'>
                                {Array.from(photosByDate.entries())
                                    .sort(
                                        ([dateA], [dateB]) =>
                                            new Date(dateB).getTime() -
                                            new Date(dateA).getTime()
                                    )
                                    .map(([dateKey, dayPhotos]) => (
                                        <div
                                            key={dateKey}
                                            className='date-group'
                                        >
                                            <h3 className='date-header'>
                                                {new Date(
                                                    dateKey
                                                ).toLocaleDateString('zh-CN', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    weekday: 'long'
                                                })}{' '}
                                                ({dayPhotos.length} 张照片)
                                            </h3>
                                            <div
                                                className='photos-grid'
                                                style={{
                                                    gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`
                                                }}
                                            >
                                                {getFilteredPhotos(dayPhotos).map(photo => (
                                                    <div
                                                        key={photo.uuid}
                                                        className={`photo-card ${hoveredPhoto === photo.uuid ? 'hovered' : ''} ${photo.marked_del ? 'marked' : ''}`}
                                                        onMouseEnter={() => setHoveredPhoto(photo.uuid)}
                                                        onMouseLeave={() => setHoveredPhoto(null)}
                                                    >
                                                        {/* 标记删除的红色❌ */}
                                                        {photo.marked_del && showMarked && (
                                                            <div className='photo-mark-overlay'>
                                                                <span className='mark-icon'>❌</span>
                                                            </div>
                                                        )}

                                                        {/* 悬浮提示 */}
                                                        {hoveredPhoto === photo.uuid && (
                                                            <div className='hover-hint'>
                                                                <span>按 D 键{photo.marked_del ? '取消标记' : '标记删除'}</span>
                                                            </div>
                                                        )}

                                                        {photo.thumbnail_path ? (
                                                            <img
                                                                src={convertFileSrc(
                                                                    photo.thumbnail_path
                                                                )}
                                                                alt={
                                                                    photo.filename
                                                                }
                                                                className='photo-thumbnail'
                                                                style={{ cursor: 'pointer' }}
                                                                onClick={() => handleImageClick(photo)}

                                                                onError={e => {
                                                                    console.error(
                                                                        `❌ 缩略图加载失败: ${photo.thumbnail_path}`
                                                                    );
                                                                    console.error(
                                                                        `转换后的URL: ${convertFileSrc(
                                                                            photo.thumbnail_path ||
                                                                            ''
                                                                        )}`
                                                                    );
                                                                    e.currentTarget.style.display =
                                                                        'none';
                                                                }}
                                                            />
                                                        ) : (
                                                            <div className='photo-placeholder'>
                                                                <span>
                                                                    无缩略图
                                                                </span>
                                                                <small
                                                                    style={{
                                                                        display:
                                                                            'block',
                                                                        fontSize:
                                                                            '10px',
                                                                        color: '#666'
                                                                    }}
                                                                >
                                                                    {
                                                                        photo.filename
                                                                    }
                                                                </small>
                                                            </div>
                                                        )}
                                                        <div className='photo-info'>
                                                            <h3>
                                                                {photo.filename}
                                                            </h3>
                                                            <p>
                                                                尺寸:{' '}
                                                                {photo.width} ×{' '}
                                                                {photo.height}
                                                            </p>
                                                            <p>
                                                                类型:{' '}
                                                                {getImageTypeDisplay(photo.mime_type)}
                                                            </p>
                                                            <p>
                                                                大小:{' '}
                                                                {(
                                                                    photo.file_size /
                                                                    1024 /
                                                                    1024
                                                                ).toFixed(
                                                                    2
                                                                )}{' '}
                                                                MB
                                                            </p>
                                                            <p>
                                                                拍摄时间:{' '}
                                                                {new Date(
                                                                    photo.date_taken *
                                                                    1000
                                                                ).toLocaleString()}
                                                            </p>

                                                            {/* 算法详情切换按钮 */}
                                                            <button
                                                                className='toggle-details-btn'
                                                                onClick={() =>
                                                                    togglePhotoDetails(
                                                                        photo.uuid
                                                                    )
                                                                }
                                                            >
                                                                {expandedPhotoDetails.has(
                                                                    photo.uuid
                                                                )
                                                                    ? '隐藏'
                                                                    : '显示'}
                                                                算法详情
                                                            </button>

                                                            {/* 算法详情展开区域 */}
                                                            {expandedPhotoDetails.has(
                                                                photo.uuid
                                                            ) && (
                                                                    <div className='photo-algorithm-details'>
                                                                        <h4>
                                                                            Hash值 (优化分析器使用)
                                                                        </h4>
                                                                        {photo.ahash && (
                                                                            <p>
                                                                                <strong>
                                                                                    AHash (第一阶段筛选):
                                                                                </strong>{' '}
                                                                                {photo.ahash}
                                                                            </p>
                                                                        )}
                                                                        {photo.phash && (
                                                                            <p>
                                                                                <strong>
                                                                                    PHash (第二阶段验证):
                                                                                </strong>{' '}
                                                                                {photo.phash}
                                                                            </p>
                                                                        )}
                                                                        {!photo.ahash && !photo.phash && (
                                                                            <p style={{ color: '#888' }}>
                                                                                暂无hash值，请重新分析照片
                                                                            </p>
                                                                        )}

                                                                        {photo.similarity_scores &&
                                                                            Object.keys(
                                                                                photo.similarity_scores
                                                                            )
                                                                                .length >
                                                                            0 && (
                                                                                <div className='similarity-scores'>
                                                                                    <h4>
                                                                                        相似度分数
                                                                                    </h4>
                                                                                    {Object.entries(
                                                                                        photo.similarity_scores
                                                                                    )
                                                                                        .slice(
                                                                                            0,
                                                                                            3
                                                                                        )
                                                                                        .map(
                                                                                            ([
                                                                                                uuid,
                                                                                                score
                                                                                            ]) => (
                                                                                                <p
                                                                                                    key={
                                                                                                        uuid
                                                                                                    }
                                                                                                >
                                                                                                    <strong>
                                                                                                        与{' '}
                                                                                                        {uuid.substring(
                                                                                                            0,
                                                                                                            8
                                                                                                        )}
                                                                                                        ...:
                                                                                                    </strong>{' '}
                                                                                                    {(
                                                                                                        score *
                                                                                                        100
                                                                                                    ).toFixed(
                                                                                                        1
                                                                                                    )}

                                                                                                    %
                                                                                                </p>
                                                                                            )
                                                                                        )}
                                                                                </div>
                                                                            )}
                                                                    </div>
                                                                )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}

                                {/* 加载更多指示器 */}
                                {isLoadingMore && (
                                    <div className='loading-more'>
                                        <div className='spinner'></div>
                                        <p>正在加载更多照片...</p>
                                    </div>
                                )}

                                {!hasMorePhotos && photos.length > 0 && (
                                    <div className='no-more-photos'>
                                        <p>已加载所有照片</p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            // 传统模式：统一网格显示
                            <div
                                className='photos-grid'
                                style={{
                                    gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`
                                }}
                            >
                                {getFilteredPhotos(photos).map(photo => (
                                    <div
                                        key={photo.uuid}
                                        className={`photo-card ${hoveredPhoto === photo.uuid ? 'hovered' : ''} ${photo.marked_del ? 'marked' : ''}`}
                                        onMouseEnter={() => setHoveredPhoto(photo.uuid)}
                                        onMouseLeave={() => setHoveredPhoto(null)}
                                    >
                                        {/* 标记删除的红色❌ */}
                                        {photo.marked_del && showMarked && (
                                            <div className='photo-mark-overlay'>
                                                <span className='mark-icon'>❌</span>
                                            </div>
                                        )}

                                        {/* 悬浮提示 */}
                                        {hoveredPhoto === photo.uuid && (
                                            <div className='hover-hint'>
                                                <span>按 D 键{photo.marked_del ? '取消标记' : '标记删除'}</span>
                                            </div>
                                        )}

                                        {photo.thumbnail_path ? (
                                            <img
                                                src={convertFileSrc(
                                                    photo.thumbnail_path
                                                )}
                                                alt={photo.filename}
                                                className='photo-thumbnail'
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleImageClick(photo)}

                                                onError={e => {
                                                    console.error(
                                                        `❌ 缩略图加载失败: ${photo.thumbnail_path}`
                                                    );
                                                    console.error(
                                                        `转换后的URL: ${convertFileSrc(
                                                            photo.thumbnail_path ||
                                                            ''
                                                        )}`
                                                    );
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        ) : (
                                            <div className='photo-placeholder'>
                                                <span>无缩略图</span>
                                                <small
                                                    style={{
                                                        display: 'block',
                                                        fontSize: '10px',
                                                        color: '#666'
                                                    }}
                                                >
                                                    {photo.filename}
                                                </small>
                                            </div>
                                        )}
                                        <div className='photo-info'>
                                            <h3>{photo.filename}</h3>
                                            <p>
                                                尺寸: {photo.width} ×{' '}
                                                {photo.height}
                                            </p>
                                            <p>
                                                类型:{' '}
                                                {getImageTypeDisplay(photo.mime_type)}
                                            </p>
                                            <p>
                                                大小:{' '}
                                                {(
                                                    photo.file_size /
                                                    1024 /
                                                    1024
                                                ).toFixed(2)}{' '}
                                                MB
                                            </p>
                                            <p>
                                                拍摄时间:{' '}
                                                {new Date(
                                                    photo.date_taken * 1000
                                                ).toLocaleString()}
                                            </p>

                                            {/* 算法详情切换按钮 */}
                                            <button
                                                className='toggle-details-btn'
                                                onClick={() =>
                                                    togglePhotoDetails(
                                                        photo.uuid
                                                    )
                                                }
                                            >
                                                {expandedPhotoDetails.has(
                                                    photo.uuid
                                                )
                                                    ? '隐藏'
                                                    : '显示'}
                                                算法详情
                                            </button>

                                            {/* 算法详情展开区域 */}
                                            {expandedPhotoDetails.has(
                                                photo.uuid
                                            ) && (
                                                    <div className='photo-algorithm-details'>
                                                        <h4>Hash值 (优化分析器使用)</h4>
                                                        {photo.ahash && (
                                                            <p>
                                                                <strong>
                                                                    AHash (第一阶段筛选):
                                                                </strong>{' '}
                                                                {photo.ahash}
                                                            </p>
                                                        )}
                                                        {photo.phash && (
                                                            <p>
                                                                <strong>
                                                                    PHash (第二阶段验证):
                                                                </strong>{' '}
                                                                {photo.phash}
                                                            </p>
                                                        )}
                                                        {!photo.ahash && !photo.phash && (
                                                            <p style={{ color: '#888' }}>
                                                                暂无hash值，请重新分析照片
                                                            </p>
                                                        )}

                                                        {photo.similarity_scores &&
                                                            Object.keys(
                                                                photo.similarity_scores
                                                            ).length > 0 && (
                                                                <div className='similarity-scores'>
                                                                    <h4>
                                                                        相似度分数
                                                                    </h4>
                                                                    {Object.entries(
                                                                        photo.similarity_scores
                                                                    )
                                                                        .slice(0, 3)
                                                                        .map(
                                                                            ([
                                                                                uuid,
                                                                                score
                                                                            ]) => (
                                                                                <p
                                                                                    key={
                                                                                        uuid
                                                                                    }
                                                                                >
                                                                                    <strong>
                                                                                        与{' '}
                                                                                        {uuid.substring(
                                                                                            0,
                                                                                            8
                                                                                        )}
                                                                                        ...:
                                                                                    </strong>{' '}
                                                                                    {(
                                                                                        score *
                                                                                        100
                                                                                    ).toFixed(
                                                                                        1
                                                                                    )}

                                                                                    %
                                                                                </p>
                                                                            )
                                                                        )}
                                                                </div>
                                                            )}
                                                    </div>
                                                )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'duplicates' && similarityGroups.length > 0 && (
                    <div className='photo-controls'>
                        <label className='photos-per-row-control'>
                            每行显示图片数量:
                            <input
                                type='range'
                                min='4'
                                max='8'
                                value={duplicatesPerRow}
                                onChange={e =>
                                    setDuplicatesPerRow(Number(e.target.value))
                                }
                                className='photos-per-row-slider'
                            />
                            <span className='photos-per-row-value'>
                                {duplicatesPerRow}
                            </span>
                        </label>
                    </div>
                )}

                {activeTab === 'duplicates' && (
                    <div className='duplicate-groups'>
                        {/* 显示统计信息 */}
                        {similarityGroups.length > 0 && (
                            <div className='similarity-stats'>
                                <p>
                                    总共找到 {similarityGroups.length} 个相似组
                                </p>
                            </div>
                        )}

                        {/* 显示相似组 - 按时间排序（新到旧） */}
                        {similarityGroups
                            .sort((a, b) => {
                                // 获取每个组中最新照片的时间
                                const getLatestPhotoTime = (group: any) => {
                                    return Math.max(...group.photos.map((p: any) => p.date_taken));
                                };
                                return getLatestPhotoTime(b) - getLatestPhotoTime(a);
                            })
                            .filter(group => {
                                // 过滤掉所有照片都被标记删除的组（除非显示已标记照片）
                                const filteredPhotos = getFilteredPhotos(group.photos);
                                return filteredPhotos.length > 0;
                            })
                            .map(group => (
                                <div
                                    key={group.group_id}
                                    className='duplicate-group'
                                >
                                    <div className='group-header'>
                                        <h3>相似组 #{group.group_id}</h3>
                                        <span>
                                            包含 {getFilteredPhotos(group.photos).length} 张照片
                                            {!showMarked && group.photos.some((p: any) => p.marked_del) &&
                                                ` (${group.photos.filter((p: any) => p.marked_del).length} 张已标记)`
                                            }
                                        </span>
                                        <span>
                                            相似度:{' '}
                                            {(group.similarity_score * 100).toFixed(
                                                1
                                            )}
                                            %
                                        </span>
                                    </div>
                                    <div
                                        className='group-photos'
                                        style={{
                                            gridTemplateColumns: `repeat(${duplicatesPerRow}, 1fr)`
                                        }}
                                    >
                                        {/* 组内照片按时间排序（新到旧） */}
                                        {getFilteredPhotos(group.photos)
                                            .sort((a: any, b: any) => b.date_taken - a.date_taken)
                                            .map((photo: any) => (
                                                <div
                                                    key={photo.uuid}
                                                    className={`group-photo ${hoveredPhoto === photo.uuid ? 'hovered' : ''} ${photo.marked_del ? 'marked' : ''}`}
                                                    onMouseEnter={() => setHoveredPhoto(photo.uuid)}
                                                    onMouseLeave={() => setHoveredPhoto(null)}
                                                >
                                                    {/* 标记删除的红色❌ */}
                                                    {photo.marked_del && showMarked && (
                                                        <div className='photo-mark-overlay'>
                                                            <span className='mark-icon'>❌</span>
                                                        </div>
                                                    )}

                                                    {/* 悬浮提示 */}
                                                    {hoveredPhoto === photo.uuid && (
                                                        <div className='hover-hint'>
                                                            <span>按 D 键{photo.marked_del ? '取消标记' : '标记删除'}</span>
                                                        </div>
                                                    )}

                                                    {photo.thumbnail_path ? (
                                                        <img
                                                            src={convertFileSrc(
                                                                photo.thumbnail_path
                                                            )}
                                                            alt={photo.filename}
                                                            className='group-thumbnail'
                                                            style={{ cursor: 'pointer' }}
                                                            onClick={() => handleImageClick(photo)}
                                                            onError={e => {
                                                                console.error(
                                                                    `Failed to load group thumbnail: ${photo.thumbnail_path}`
                                                                );
                                                                e.currentTarget.style.display =
                                                                    'none';
                                                            }}
                                                        />
                                                    ) : (
                                                        <div className='group-placeholder'>
                                                            <span>无缩略图</span>
                                                        </div>
                                                    )}
                                                    <div className='group-photo-info'>
                                                        <h4>{photo.filename}</h4>
                                                        <p>
                                                            尺寸: {photo.width} ×{' '}
                                                            {photo.height}
                                                        </p>
                                                        <p>
                                                            类型:{' '}
                                                            {getImageTypeDisplay(photo.mime_type)}
                                                        </p>
                                                        <p>
                                                            大小:{' '}
                                                            {(
                                                                photo.file_size /
                                                                1024 /
                                                                1024
                                                            ).toFixed(2)}{' '}
                                                            MB
                                                        </p>
                                                        <p>
                                                            拍摄时间:{' '}
                                                            {new Date(
                                                                photo.date_taken * 1000
                                                            ).toLocaleString()}
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            ))}

                        {/* 近似组界面的加载状态指示器 */}
                        {batchLoadingEnabled && isLoadingMore && (
                            <div className='loading-more'>
                                <div className='loading-spinner'></div>
                                <p>正在加载更多照片并重新分析相似组...</p>
                            </div>
                        )}

                        {batchLoadingEnabled && !hasMorePhotos && similarityGroups.length > 0 && (
                            <div className='no-more-groups'>
                                <p>✅ 已加载所有照片，相似组分析完成</p>
                            </div>
                        )}
                    </div>
                )}

                {!selectedPath && !isLoading && (
                    <div className='welcome'>
                        <h2>欢迎使用照片缩略图管理器</h2>
                        <p>点击"选择照片库/目录"开始浏览和管理您的照片</p>
                    </div>
                )}
            </main>

            {/* 全屏图片显示模态框 */}
            {fullscreenImage && (
                <div className='fullscreen-overlay' onClick={closeFullscreen}>
                    <div className='fullscreen-container'>
                        <button
                            className='fullscreen-close'
                            onClick={closeFullscreen}
                            title="关闭 (ESC)"
                        >
                            ×
                        </button>
                        <img
                            src={fullscreenImage.src}
                            alt={fullscreenImage.filename}
                            className='fullscreen-image'
                            onClick={(e) => e.stopPropagation()} // 防止点击图片时关闭模态框
                            onError={(e) => {
                                console.error(`❌ 全屏图片加载失败: ${fullscreenImage.src}`);
                                e.currentTarget.style.display = 'none';
                            }}
                        />
                        <div className='fullscreen-info'>
                            <p>{fullscreenImage.filename}</p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default App;