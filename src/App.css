/* 基础样式 */
.logo.vite:hover {
    filter: drop-shadow(0 0 2em #747bff);
}

.logo.react:hover {
    filter: drop-shadow(0 0 2em #61dafb);
}

:root {
    font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;

    color: #0f0f0f;
    background-color: #f6f6f6;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
}

.container {
    margin: 0;
    padding-top: 10vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.logo {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: 0.75s;
}

.logo.tauri:hover {
    filter: drop-shadow(0 0 2em #24c8db);
}

.row {
    display: flex;
    justify-content: center;
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}

a:hover {
    color: #535bf2;
}

h1 {
    text-align: center;
}

input,
button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    color: #0f0f0f;
    background-color: #ffffff;
    transition: border-color 0.25s;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}

button {
    cursor: pointer;
}

button:hover {
    border-color: #396cd8;
}

button:active {
    border-color: #396cd8;
    background-color: #e8e8e8;
}

input,
button {
    outline: none;
}

#greet-input {
    margin-right: 5px;
}

@media (prefers-color-scheme: dark) {
    :root {
        color: #f6f6f6;
        background-color: #2f2f2f;
    }

    a:hover {
        color: #24c8db;
    }

    input,
    button {
        color: #ffffff;
        background-color: #0f0f0f98;
    }

    button:active {
        background-color: #0f0f0f69;
    }
}

/* 应用特定样式 */
.app {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.app-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.header-top {
    position: relative;
    display: inline-block;
}

.devtools-btn:hover {
    background-color: #005a9e !important;
}

.app-header h1 {
    margin: 0;
    color: #333;
    font-size: 2rem;
}

.app-subtitle {
    margin: 0.5rem 0 0 0;
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

.path-selection {
    margin-top: 20px;
}

.header-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
}

.manual-path-section {
    text-align: center;
    margin: 20px 0;
}

.manual-path-section p {
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9rem;
}

.manual-path-input {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.manual-path-input input {
    min-width: 300px;
    max-width: 500px;
    flex: 1;
}

.selected-path {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    background: #f0f8ff;
    border: 1px solid #b0d4f1;
    border-radius: 8px;
}

.selected-path p {
    margin: 0 0 10px 0;
    font-size: 0.9rem;
    color: #333;
    word-break: break-all;
}

.tab-navigation {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
}

.tab-navigation button {
    padding: 10px 20px;
    border: 1px solid #ddd;
    background: #f5f5f5;
    cursor: pointer;
    border-radius: 5px;
}

.tab-navigation button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}





/* 照片控制面板样式 */
.photo-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.photos-per-row-control {
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 500;
    color: #495057;
}

.photos-per-row-slider {
    width: 150px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.photos-per-row-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.photos-per-row-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.photos-per-row-value {
    min-width: 20px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
    background: #e7f3ff;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 时间范围滑块样式 */
.time-range-slider {
    width: 200px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.time-range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #28a745;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.time-range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #28a745;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* 算法参数控制面板样式 */
.algorithm-config-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 智能加载配置样式 */
.config-subsection {
    margin-left: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.config-description {
    font-size: 0.85rem;
    color: #6c757d;
    margin-left: 8px;
}

.loading-stats {
    margin-top: 15px;
    padding: 15px;
    background: #e7f3ff;
    border-radius: 6px;
    border: 1px solid #b3d9ff;
}

.loading-stats h5 {
    margin: 0 0 10px 0;
    color: #0056b3;
    font-size: 0.9rem;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #d1ecf1;
}

.stat-item.searching {
    background: #fff3cd;
    border-color: #ffeaa7;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

.stat-label {
    font-size: 0.8rem;
    color: #495057;
    font-weight: 500;
}

.stat-value {
    font-size: 0.85rem;
    color: #007bff;
    font-weight: 600;
}

.algorithm-config-panel h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.2rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.config-section {
    margin-bottom: 25px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.config-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.config-item {
    margin-bottom: 15px;
}

.config-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    color: #495057;
}

.config-item input[type='number'] {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.config-item input[type='range'] {
    flex: 1;
    max-width: 200px;
}

.config-help {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.config-value {
    min-width: 40px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
    background: #e7f3ff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.hash-algorithms {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-label:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.checkbox-label input[type='checkbox'] {
    margin: 0;
}

.algorithm-description {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.config-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.apply-config-btn,
.reset-config-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.apply-config-btn {
    background: #007bff;
    color: white;
}

.apply-config-btn:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
}

.apply-config-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.reset-config-btn {
    background: #6c757d;
    color: white;
}

.reset-config-btn:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* 照片算法详情样式 */
.toggle-details-btn {
    margin-top: 8px;
    padding: 4px 8px;
    font-size: 0.7rem;
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 100%;
}

.toggle-details-btn:hover {
    background: #138496;
}

.photo-algorithm-details {
    margin-top: 8px;
    padding: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 0.65rem;
}

.photo-algorithm-details h4 {
    margin: 0 0 6px 0;
    font-size: 0.7rem;
    color: #495057;
    font-weight: 600;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 2px;
}

.photo-algorithm-details p {
    margin: 3px 0;
    line-height: 1.2;
    word-break: break-all;
}

.similarity-scores {
    margin-top: 8px;
    padding-top: 6px;
    border-top: 1px solid #dee2e6;
}

/* 分批加载和按日期分组样式 */
.photos-container {
    width: 100%;
}

.photos-by-date {
    width: 100%;
}

.date-group {
    margin-bottom: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.date-header {
    margin: 0 0 20px 0;
    padding: 10px 15px;
    background: #007bff;
    color: white;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.loading-more {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    margin: 20px 0;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.loading-more .spinner {
    width: 30px;
    height: 30px;
    margin-bottom: 15px;
}

.loading-more p {
    margin: 0;
    color: #6c757d;
    font-style: italic;
}

.no-more-photos {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px 20px;
    margin: 20px 0;
    background: #e9ecef;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.no-more-photos p {
    margin: 0;
    color: #6c757d;
    font-weight: 500;
    font-style: italic;
}

/* 优化分析配置样式 */
.optimization-config {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.optimization-config .config-item {
    margin-bottom: 12px;
}

.optimization-config .config-item:last-child {
    margin-bottom: 0;
}

.config-help {
    font-size: 0.85rem;
    color: #6c757d;
    margin-left: 8px;
    font-style: italic;
}

.config-value {
    display: inline-block;
    min-width: 40px;
    text-align: center;
    font-weight: 600;
    color: #007bff;
    margin-left: 8px;
    padding: 2px 6px;
    background: #e3f2fd;
    border-radius: 3px;
    font-size: 0.9rem;
}

.optimization-config input[type='number'] {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    margin-left: 8px;
}

.optimization-config input[type='range'] {
    width: 120px;
    margin: 0 8px;
}

.optimization-config label {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    font-weight: 500;
}

/* 优化分析结果显示 */
.efficiency-stats {
    margin-top: 15px;
    padding: 12px;
    background: #e8f5e8;
    border-radius: 6px;
    border: 1px solid #c3e6c3;
}

.efficiency-stats h4 {
    margin: 0 0 8px 0;
    color: #155724;
    font-size: 1rem;
}

.efficiency-stats p {
    margin: 4px 0;
    font-size: 0.9rem;
    color: #155724;
}

.performance-highlight {
    font-weight: 600;
    color: #007bff;
}

.photos-grid {
    display: grid;
    /* grid-template-columns 将通过内联样式动态设置 */
    gap: 12px;
    margin-top: 20px;
    grid-auto-rows: max-content;
    padding: 0 10px;
}

.photo-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    break-inside: avoid;
    display: flex;
    flex-direction: column;
}

.photo-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.photo-card img {
    width: 100%;
    height: auto;
    min-height: 120px;
    max-height: 200px;
    object-fit: cover;
    border-radius: 0;
    display: block;
    background: #f8f9fa;
    flex-shrink: 0;
}

.photo-info {
    padding: 8px 10px;
    font-size: 0.75rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.photo-info h3 {
    margin: 0 0 4px 0;
    font-size: 0.8rem;
    color: #333;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-info p {
    margin: 2px 0;
    color: #666;
    line-height: 1.2;
    font-size: 0.7rem;
}

.photo-placeholder {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.similarity-groups {
    margin-top: 20px;
}

.similarity-group {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background: white;
}

.group-header {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2rem;
    color: #666;
}

/* 新增样式 - 自动检测 Photos Library */
.library-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 10px;
}

.library-actions button {
    flex: 1;
    min-width: 150px;
    max-width: 200px;
}

.no-default-library {
    color: #666;
    font-style: italic;
    margin-bottom: 1rem;
    text-align: center;
}

.loading-status {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #e3f2fd;
    border-radius: 8px;
    text-align: center;
}

.loading-status p {
    margin: 0;
    color: #1976d2;
    font-weight: bold;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.photo-details {
    margin-bottom: 30px;
}

.photo-details h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.photo-details p {
    margin: 8px 0;
    word-break: break-all;
}

.thumbnails-list h3 {
    margin-bottom: 20px;
    color: #333;
}

.thumbnails-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.thumbnail-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background: #f9f9f9;
}

.thumbnail-preview {
    width: 100%;
    height: 150px;
    background: #eee;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
}

.thumbnail-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.thumbnail-missing {
    color: #666;
    font-style: italic;
}

.thumbnail-info p {
    margin: 5px 0;
    font-size: 14px;
    word-break: break-all;
}

.thumbnail-info strong {
    color: #333;
}

/* 相似照片组样式 */
.group-photos {
    display: grid;
    /* grid-template-columns 将通过内联样式动态设置 */
    gap: 12px;
    margin-top: 15px;
}

.group-photo {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    break-inside: avoid;
    display: flex;
    flex-direction: column;
}

.group-photo:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.group-thumbnail {
    width: 100%;
    height: auto;
    min-height: 100px;
    max-height: 160px;
    object-fit: cover;
    display: block;
    flex-shrink: 0;
}

.group-photo-info {
    padding: 8px 10px;
    font-size: 0.75rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.group-photo-info h4 {
    margin: 0 0 4px 0;
    font-size: 0.8rem;
    color: #333;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.group-photo-info p {
    margin: 2px 0;
    color: #666;
    line-height: 1.2;
    font-size: 0.7rem;
}

.group-placeholder {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

/* 进度条样式 */
.progress-info {
    margin-top: 15px;
}

.progress-info p {
    margin: 5px 0;
    font-size: 0.9rem;
}

.progress-bar-container {
    width: 100%;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    height: 100%;
    background-color: #4caf50;
    transition: width 0.3s ease;
}

/* 清空数据库按钮样式 */
.clear-database-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.2);
}

.clear-database-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
    transform: translateY(-1px);
}

.clear-database-btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.2);
}

.clear-database-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* 算法说明和警告样式 */
.algorithm-explanation {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 12px;
    margin: 10px 0;
    font-size: 0.9rem;
}

.algorithm-explanation p {
    margin: 4px 0;
    color: #0c5460;
}

.algorithm-explanation strong {
    color: #0a4c57;
}

.threshold-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 10px;
    margin: 10px 0;
    color: #856404;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 全屏图片显示样式 */
.fullscreen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    cursor: pointer;
}

.fullscreen-container {
    position: relative;
    max-width: 95vw;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: default;
}

.fullscreen-image {
    max-width: 100%;
    max-height: 85vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    cursor: default;
}

.fullscreen-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    transition: all 0.2s ease;
    z-index: 2001;
}

.fullscreen-close:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.fullscreen-info {
    margin-top: 15px;
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 20px;
    max-width: 80%;
}

.fullscreen-info p {
    margin: 0;
    font-size: 14px;
    word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .fullscreen-close {
        top: -40px;
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .fullscreen-image {
        max-height: 80vh;
    }

    .fullscreen-info {
        margin-top: 10px;
        padding: 8px 15px;
        font-size: 12px;
    }

    .photos-grid {
        gap: 8px;
        padding: 0 5px;
    }

    .photo-card img {
        min-height: 100px;
        max-height: 150px;
    }

    .photo-info {
        padding: 6px 8px;
        font-size: 0.7rem;
    }

    .photo-info h3 {
        font-size: 0.75rem;
    }

    .photo-info p {
        font-size: 0.65rem;
    }

    .photo-controls {
        margin: 15px 0;
        padding: 10px;
    }

    .photos-per-row-control {
        gap: 10px;
        font-size: 0.9rem;
    }

    .photos-per-row-slider {
        width: 120px;
    }

    .group-photos {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .group-thumbnail {
        min-height: 100px;
        max-height: 150px;
    }
}

@media (max-width: 576px) {
    .photos-grid {
        gap: 6px;
        padding: 0;
    }

    .photo-card img {
        min-height: 80px;
        max-height: 120px;
    }

    .photo-info {
        padding: 4px 6px;
    }

    .photo-info h3 {
        font-size: 0.7rem;
        margin-bottom: 2px;
    }

    .photo-info p {
        font-size: 0.6rem;
        margin: 1px 0;
    }

    .group-photos {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* 响应式设计 - 在小屏幕上减少列数 */
@media (max-width: 1400px) {
    .photos-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (max-width: 1200px) {
    .photos-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .photos-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .photos-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .photos-grid {
        grid-template-columns: 1fr;
    }
}



/* 相似照片组样式 */
.group-photos {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.group-photo {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    background: white;
}

.group-photo:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.group-thumbnail {
    width: 100%;
    height: auto;
    min-height: 120px;
    max-height: 200px;
    object-fit: cover;
    display: block;
}

.group-placeholder {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        padding: 15px;
    }

    .photo-card img {
        min-height: 180px;
        max-height: 300px;
    }

    .group-photos {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .group-thumbnail {
        min-height: 100px;
        max-height: 150px;
    }
}

@media (max-width: 480px) {
    .photos-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .group-photos {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* 无限滚动相关样式 */
.infinite-scroll-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 20px;
}

.groups-per-page-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px;
}

.groups-per-page-slider {
    width: 80px;
}

.groups-per-page-value {
    min-width: 20px;
    text-align: center;
    font-weight: bold;
    color: #007acc;
}

.similarity-stats {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    color: #666;
    border-left: 4px solid #007acc;
}



.no-more-groups {
    color: #28a745;
    font-weight: bold;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

/* 智能缓存控制样式 */
.smart-cache-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 20px;
    font-weight: 500;
    color: #495057;
}

.smart-cache-days-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px;
    font-weight: 500;
    color: #495057;
}

.smart-cache-days-slider {
    width: 80px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.smart-cache-days-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007acc;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 122, 204, 0.3);
}

.smart-cache-days-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007acc;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 122, 204, 0.3);
}

.smart-cache-days-value {
    min-width: 20px;
    text-align: center;
    font-weight: bold;
    color: #007acc;
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 智能缓存加载更多按钮样式 */
.load-more-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #007acc;
}

.load-more-btn {
    background: linear-gradient(135deg, #007acc, #0056b3);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
}

.load-more-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 122, 204, 0.4);
}

.load-more-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 122, 204, 0.3);
}

.load-more-hint {
    margin-top: 8px;
    color: #6c757d;
    font-size: 14px;
    text-align: center;
}

/* 清空缓存按钮样式 */
.clear-cache-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-left: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.clear-cache-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.clear-cache-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

.clear-cache-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 照片标记删除相关样式 */

/* 照片卡片悬浮状态 */
.photo-card.hovered {
    border: 2px solid #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transform: translateY(-2px);
    transition: all 0.2s ease;
}

/* 已标记的照片卡片 */
.photo-card.marked {
    border: 2px solid #dc3545;
    opacity: 0.7;
}

/* 标记删除的红色❌覆盖层 */
.photo-mark-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    background: rgba(220, 53, 69, 0.9);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.mark-icon {
    font-size: 16px;
    color: white;
    font-weight: bold;
}

/* 悬浮提示 */
.hover-hint {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    pointer-events: none;
}

/* Show Marked 切换按钮 */
.show-marked-button {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.show-marked-button:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
}

.show-marked-button.active {
    background: #dc3545;
    color: white;
}

.show-marked-button.active:hover {
    background: #c82333;
}

/* 确保照片卡片有相对定位，以便绝对定位的覆盖层正确显示 */
.photo-card {
    position: relative;
    transition: all 0.2s ease;
}

/* 悬浮时的边框高亮效果 */
.photo-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

/* 标记状态的特殊样式 */
.photo-card.marked:hover {
    border-color: #dc3545;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}