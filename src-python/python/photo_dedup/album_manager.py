"""
系统相册管理模块 - 使用osxphotos.PhotosAlbum管理"Photo Duplication Remove/marked delete"相簿
"""

import logging
from typing import List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

# 相册配置常量
FOLDER_NAME = "Photo Duplication Remove"
ALBUM_NAME = "marked delete"


class AlbumManager:
    """管理系统相册的类，用于处理标记删除的照片"""
    
    def __init__(self):
        self.folder_name = FOLDER_NAME
        self.album_name = ALBUM_NAME
        self._album = None
        self._photos_album = None
        
    def _get_photos_album(self):
        """获取PhotosAlbum实例，延迟初始化"""
        if self._photos_album is None:
            try:
                from osxphotos.photosalbum import PhotosAlbum
                self._photos_album = PhotosAlbum()
                logger.info("✅ PhotosAlbum 初始化成功")
            except ImportError as e:
                logger.error(f"❌ 无法导入osxphotos.PhotosAlbum: {e}")
                raise
            except Exception as e:
                logger.error(f"❌ PhotosAlbum 初始化失败: {e}")
                raise
        return self._photos_album
    
    def ensure_album_exists(self) -> bool:
        """
        确保相册文件夹和相簿存在，如果不存在则创建
        
        Returns:
            bool: 操作是否成功
        """
        try:
            photos_album = self._get_photos_album()
            
            # 检查文件夹是否存在
            folder_exists = False
            try:
                folders = photos_album.folder_names
                folder_exists = self.folder_name in folders
                logger.info(f"📁 文件夹 '{self.folder_name}' 存在状态: {folder_exists}")
            except Exception as e:
                logger.warning(f"⚠️ 检查文件夹时出错: {e}")
            
            # 如果文件夹不存在，创建文件夹
            if not folder_exists:
                logger.info(f"📁 创建文件夹: {self.folder_name}")
                try:
                    photos_album.create_folder(self.folder_name)
                    logger.info(f"✅ 文件夹 '{self.folder_name}' 创建成功")
                except Exception as e:
                    logger.error(f"❌ 创建文件夹失败: {e}")
                    return False
            
            # 检查相簿是否存在
            album_path = f"{self.folder_name}/{self.album_name}"
            album_exists = False
            try:
                albums = photos_album.album_names
                album_exists = album_path in albums or self.album_name in albums
                logger.info(f"📸 相簿 '{album_path}' 存在状态: {album_exists}")
            except Exception as e:
                logger.warning(f"⚠️ 检查相簿时出错: {e}")
            
            # 如果相簿不存在，创建相簿
            if not album_exists:
                logger.info(f"📸 创建相簿: {album_path}")
                try:
                    # 尝试在文件夹下创建相簿
                    photos_album.create_album(self.album_name, folder=self.folder_name)
                    logger.info(f"✅ 相簿 '{album_path}' 创建成功")
                except Exception as e:
                    logger.warning(f"⚠️ 在文件夹下创建相簿失败，尝试直接创建: {e}")
                    try:
                        # 如果在文件夹下创建失败，尝试直接创建
                        photos_album.create_album(self.album_name)
                        logger.info(f"✅ 相簿 '{self.album_name}' 创建成功")
                    except Exception as e2:
                        logger.error(f"❌ 创建相簿失败: {e2}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 确保相册存在时出错: {e}")
            return False
    
    def add_photo_to_album(self, photo_uuid: str) -> bool:
        """
        将照片添加到标记删除相簿
        
        Args:
            photo_uuid: 照片的UUID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 确保相册存在
            if not self.ensure_album_exists():
                logger.error("❌ 无法确保相册存在，添加照片失败")
                return False
            
            photos_album = self._get_photos_album()
            
            # 尝试不同的相簿路径
            album_paths = [
                f"{self.folder_name}/{self.album_name}",
                self.album_name
            ]
            
            success = False
            for album_path in album_paths:
                try:
                    logger.info(f"📸 尝试将照片 {photo_uuid} 添加到相簿 '{album_path}'")
                    photos_album.add_to_album(photo_uuid, album_path)
                    logger.info(f"✅ 照片 {photo_uuid} 已添加到相簿 '{album_path}'")
                    success = True
                    break
                except Exception as e:
                    logger.warning(f"⚠️ 添加到相簿 '{album_path}' 失败: {e}")
                    continue
            
            if not success:
                logger.error(f"❌ 无法将照片 {photo_uuid} 添加到任何相簿")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加照片到相簿时出错: {e}")
            return False
    
    def remove_photo_from_album(self, photo_uuid: str) -> bool:
        """
        从标记删除相簿中移除照片
        
        Args:
            photo_uuid: 照片的UUID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            photos_album = self._get_photos_album()
            
            # 尝试不同的相簿路径
            album_paths = [
                f"{self.folder_name}/{self.album_name}",
                self.album_name
            ]
            
            success = False
            for album_path in album_paths:
                try:
                    logger.info(f"📸 尝试从相簿 '{album_path}' 移除照片 {photo_uuid}")
                    photos_album.remove_from_album(photo_uuid, album_path)
                    logger.info(f"✅ 照片 {photo_uuid} 已从相簿 '{album_path}' 移除")
                    success = True
                    break
                except Exception as e:
                    logger.warning(f"⚠️ 从相簿 '{album_path}' 移除失败: {e}")
                    continue
            
            if not success:
                logger.warning(f"⚠️ 无法从任何相簿移除照片 {photo_uuid}（可能照片不在相簿中）")
                # 对于移除操作，即使失败也返回True，因为目标状态已达成
                return True
                
            return True
            
        except Exception as e:
            logger.error(f"❌ 从相簿移除照片时出错: {e}")
            return False
    
    def get_album_photos(self) -> List[str]:
        """
        获取标记删除相簿中的所有照片UUID
        
        Returns:
            List[str]: 照片UUID列表
        """
        try:
            photos_album = self._get_photos_album()
            
            # 尝试不同的相簿路径
            album_paths = [
                f"{self.folder_name}/{self.album_name}",
                self.album_name
            ]
            
            for album_path in album_paths:
                try:
                    logger.info(f"📸 尝试获取相簿 '{album_path}' 中的照片")
                    photos = photos_album.photos_in_album(album_path)
                    logger.info(f"✅ 从相簿 '{album_path}' 获取到 {len(photos)} 张照片")
                    return [photo.uuid for photo in photos]
                except Exception as e:
                    logger.warning(f"⚠️ 从相簿 '{album_path}' 获取照片失败: {e}")
                    continue
            
            logger.warning("⚠️ 无法从任何相簿获取照片列表")
            return []
            
        except Exception as e:
            logger.error(f"❌ 获取相簿照片时出错: {e}")
            return []


# 全局相册管理器实例
_album_manager = None


def get_album_manager() -> AlbumManager:
    """获取全局相册管理器实例"""
    global _album_manager
    if _album_manager is None:
        _album_manager = AlbumManager()
    return _album_manager


def ensure_marked_delete_album() -> bool:
    """确保标记删除相册存在的便捷函数"""
    manager = get_album_manager()
    return manager.ensure_album_exists()


def add_photo_to_marked_delete_album(photo_uuid: str) -> bool:
    """将照片添加到标记删除相册的便捷函数"""
    manager = get_album_manager()
    return manager.add_photo_to_album(photo_uuid)


def remove_photo_from_marked_delete_album(photo_uuid: str) -> bool:
    """从标记删除相册移除照片的便捷函数"""
    manager = get_album_manager()
    return manager.remove_photo_from_album(photo_uuid)


def get_marked_delete_album_photos() -> List[str]:
    """获取标记删除相册中所有照片的便捷函数"""
    manager = get_album_manager()
    return manager.get_album_photos()
