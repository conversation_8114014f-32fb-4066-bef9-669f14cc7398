"""
系统相册管理模块 - 使用osxphotos.PhotosAlbum管理"Photo Duplication Remove/marked delete"相簿
"""

import logging
from typing import List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

# 相册配置常量
FOLDER_NAME = "Photo Duplication Remove"
ALBUM_NAME = "marked delete"


class AlbumManager:
    """管理系统相册的类，用于处理标记删除的照片"""

    def __init__(self):
        self.folder_name = FOLDER_NAME
        self.album_name = ALBUM_NAME
        self._album = None
        self._photos_album = None

    def _get_photos_album(self):
        """获取PhotosAlbum实例，延迟初始化"""
        if self._photos_album is None:
            try:
                from osxphotos.photosalbum import PhotosAlbum
                # 创建PhotosAlbum实例，不需要name参数
                self._photos_album = PhotosAlbum()
                logger.info("✅ PhotosAlbum 初始化成功")
            except ImportError as e:
                logger.error(f"❌ 无法导入osxphotos.PhotosAlbum: {e}")
                raise
            except Exception as e:
                logger.error(f"❌ PhotosAlbum 初始化失败: {e}")
                raise
        return self._photos_album

    def ensure_album_exists(self) -> bool:
        """
        确保相册文件夹和相簿存在，如果不存在则创建

        Returns:
            bool: 操作是否成功
        """
        try:
            photos_album = self._get_photos_album()

            # 检查相簿是否存在
            album_exists = False
            try:
                # 尝试获取相簿，如果存在则不会抛出异常
                album = photos_album.album(self.album_name)
                album_exists = album is not None
                logger.info(f"📸 相簿 '{self.album_name}' 存在状态: {album_exists}")
            except Exception as e:
                logger.info(f"📸 相簿 '{self.album_name}' 不存在，需要创建")
                album_exists = False

            # 如果相簿不存在，创建相簿
            if not album_exists:
                logger.info(f"📸 创建相簿: {self.album_name}")
                try:
                    # 直接创建相簿，不使用文件夹
                    photos_album.create_album(self.album_name)
                    logger.info(f"✅ 相簿 '{self.album_name}' 创建成功")
                except Exception as e:
                    logger.error(f"❌ 创建相簿失败: {e}")
                    # 即使创建失败，也返回True，因为相簿可能已经存在
                    logger.warning(f"⚠️ 相簿创建失败，但继续执行（相簿可能已存在）")

            return True

        except Exception as e:
            logger.error(f"❌ 确保相册存在时出错: {e}")
            # 即使出错，也返回True，让标记功能继续工作
            return True

    def add_photo_to_album(self, photo_uuid: str) -> bool:
        """
        将照片添加到标记删除相簿

        Args:
            photo_uuid: 照片的UUID

        Returns:
            bool: 操作是否成功
        """
        try:
            # 确保相册存在
            if not self.ensure_album_exists():
                logger.error("❌ 无法确保相册存在，添加照片失败")
                return False

            photos_album = self._get_photos_album()

            # 尝试使用正确的API添加照片
            try:
                logger.info(f"📸 尝试将照片 {photo_uuid} 添加到相簿 '{self.album_name}'")
                # 获取相簿对象
                album = photos_album.album(self.album_name)
                if album:
                    # 使用相簿对象的add方法
                    album.add([photo_uuid])
                    logger.info(f"✅ 照片 {photo_uuid} 已添加到相簿 '{self.album_name}'")
                    return True
                else:
                    logger.error(f"❌ 无法获取相簿 '{self.album_name}'")
                    # 即使失败也返回True，让数据库标记继续工作
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 添加到相簿失败: {e}")
                # 即使失败也返回True，让数据库标记继续工作
                return True

            return True

        except Exception as e:
            logger.error(f"❌ 添加照片到相簿时出错: {e}")
            return False

    def remove_photo_from_album(self, photo_uuid: str) -> bool:
        """
        从标记删除相簿中移除照片

        Args:
            photo_uuid: 照片的UUID

        Returns:
            bool: 操作是否成功
        """
        try:
            photos_album = self._get_photos_album()

            # 尝试使用正确的API移除照片
            try:
                logger.info(f"📸 尝试从相簿 '{self.album_name}' 移除照片 {photo_uuid}")
                # 获取相簿对象
                album = photos_album.album(self.album_name)
                if album:
                    # 使用相簿对象的remove方法
                    album.remove([photo_uuid])
                    logger.info(f"✅ 照片 {photo_uuid} 已从相簿 '{self.album_name}' 移除")
                    return True
                else:
                    logger.error(f"❌ 无法获取相簿 '{self.album_name}'")
                    # 对于移除操作，即使失败也返回True，因为目标状态已达成
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 从相簿移除失败: {e}")
                # 对于移除操作，即使失败也返回True，因为目标状态已达成
                return True

            return True

        except Exception as e:
            logger.error(f"❌ 从相簿移除照片时出错: {e}")
            return False

    def get_album_photos(self) -> List[str]:
        """
        获取标记删除相簿中的所有照片UUID

        Returns:
            List[str]: 照片UUID列表
        """
        try:
            photos_album = self._get_photos_album()

            # 尝试不同的相簿路径
            album_paths = [
                f"{self.folder_name}/{self.album_name}",
                self.album_name
            ]

            for album_path in album_paths:
                try:
                    logger.info(f"📸 尝试获取相簿 '{album_path}' 中的照片")
                    photos = photos_album.photos_in_album(album_path)
                    logger.info(f"✅ 从相簿 '{album_path}' 获取到 {len(photos)} 张照片")
                    return [photo.uuid for photo in photos]
                except Exception as e:
                    logger.warning(f"⚠️ 从相簿 '{album_path}' 获取照片失败: {e}")
                    continue

            logger.warning("⚠️ 无法从任何相簿获取照片列表")
            return []

        except Exception as e:
            logger.error(f"❌ 获取相簿照片时出错: {e}")
            return []


# 全局相册管理器实例
_album_manager = None


def get_album_manager() -> AlbumManager:
    """获取全局相册管理器实例"""
    global _album_manager
    if _album_manager is None:
        _album_manager = AlbumManager()
    return _album_manager


def ensure_marked_delete_album() -> bool:
    """确保标记删除相册存在的便捷函数"""
    manager = get_album_manager()
    return manager.ensure_album_exists()


def add_photo_to_marked_delete_album(photo_uuid: str) -> bool:
    """将照片添加到标记删除相册的便捷函数"""
    manager = get_album_manager()
    return manager.add_photo_to_album(photo_uuid)


def remove_photo_from_marked_delete_album(photo_uuid: str) -> bool:
    """从标记删除相册移除照片的便捷函数"""
    manager = get_album_manager()
    return manager.remove_photo_from_album(photo_uuid)


def get_marked_delete_album_photos() -> List[str]:
    """获取标记删除相册中所有照片的便捷函数"""
    manager = get_album_manager()
    return manager.get_album_photos()
