<!--
 * @Author: Rais
 * @Date: 2025-07-29 21:38:18
 * @LastEditTime: 2025-07-29 21:38:34
 * @LastEditors: Rais
 * @Description:
-->

支持当鼠标悬浮在某照片缩略图上的时候按 d键 将照片 标记,右上角出现一个红色的 ❌  ,为marked_del标记,
再次按d 会取消标记,只有在 show marked 按钮是 true时候才显示 marked_del标记的照片
默认不显示 marked_del标记的照片,所以刚被标记的照片会自动隐藏
当一个照片被标记为marked_del的时候,程序要将照片添加到系统相册 文件夹"Photo Duplication Remove"相簿"marked delete",程序启动时,如果没有这个相簿和文件夹则创建
当一个照片被标记为marked_del的时候,程序要将照片属性marked_del设置为true,保存到sql缓存数据库.
当一个照片取消marked_del标记的时候,程序要将照片从系统相册 文件夹"Photo Duplication Remove"相簿"marked delete"中移除,
程序要将照片属性marked_del设置为false,保存到sql缓存数据库.
界面置顶横幅要有一个按钮(toggle 样式)叫"show marked",快捷键 h 可切换显示,  切换 marked_del标记的照片是否显示,当 true时候,显示marked_del标记的照片,照片右上角有一个红色的 ❌ ,表示已经被标记,当false的时候,有marked_del的照片就会自动隐藏

```python
"""Example that shows how to add PhotoInfo objects to an album in Photos"""

from osxphotos import PhotosDB
from osxphotos.photosalbum import PhotosAlbum

# If album exists it will be used, otherwise it will be created
album = PhotosAlbum("Best Photos")
best_photos = [p for p in PhotosDB(verbose=print).photos() if p.score.overall > 0.9]

# use album.add() or album.append() to add a single photo
# use album.update() or album.extend() to add an iterable of photos
album.extend(best_photos)
print(f"Added {len(best_photos)} photos to album {album.name}")
print(f"Album contains {len(album.photos())} photos")
```

核心实现机制

1. 相簿创建逻辑
相簿的创建通过 album_by_path 函数实现： photosalbum.py:61-93

该函数会：

检查相簿是否已存在（包括Unicode变体）
如果不存在则自动创建
支持文件夹层级结构
2. 照片添加方法
PhotosAlbum 类提供多种添加照片的方法：

add() 或 append()：添加单张照片 photosalbum.py:125-130
update() 或 extend()：批量添加照片 photosalbum.py:132-146
3. 文件夹支持
如果需要在文件夹中创建相簿，可以使用 split_folder 参数： photosalbum.py:111-114

实际使用场景
在导入功能中，系统使用 PhotosAlbumPhotoScript 来添加照片到相簿： import_cli.py:1434-1438

Notes
PhotosAlbum 类使用 PhotoScript 库与 Photos.app 交互
相簿创建是非破坏性的 - 如果相簿已存在，会直接使用现有相簿
支持Unicode字符的相簿名称，并会检查各种Unicode变体以避免重复创建
照片通过UUID进行识别和添加
该功能仅在macOS上可用，因为需要与Photos.app交互
