"""
照片标记删除功能的单元测试
"""

import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock
from datetime import datetime

# 添加项目路径到Python路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'python'))

from photo_dedup.database import (
    initialize_database,
    update_photo_marked_status,
    get_marked_photos,
    get_unmarked_photos,
    get_photo_marked_status,
    get_marked_photos_count,
    Photo,
    Library
)
from photo_dedup.collector import (
    mark_photo_for_deletion,
    unmark_photo_for_deletion,
    filter_photos_by_marked_status,
    get_photo_mark_status,
    get_marked_photos_summary
)
from photo_dedup.models import PhotoInfo


class TestPhotoMarking(unittest.TestCase):
    """照片标记功能测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # 初始化数据库
        self.Session = initialize_database(f"sqlite:///{self.temp_db.name}")
        self.session = self.Session()
        
        # 创建测试库
        self.test_library = Library(
            name="Test Library",
            path="/test/path",
            library_type="apple_photos"
        )
        self.session.add(self.test_library)
        self.session.commit()
        
        # 创建测试照片
        self.test_photo = Photo(
            uuid="test-uuid-123",
            filename="test_photo.jpg",
            file_path="/test/path/test_photo.jpg",
            file_size=1024000,
            date_taken=datetime.now(),
            library_id=self.test_library.id,
            marked_del=False
        )
        self.session.add(self.test_photo)
        self.session.commit()
        
    def tearDown(self):
        """测试后的清理"""
        self.session.close()
        os.unlink(self.temp_db.name)
    
    def test_update_photo_marked_status(self):
        """测试更新照片标记状态"""
        # 测试标记照片
        result = update_photo_marked_status(self.session, "test-uuid-123", True)
        self.assertTrue(result)
        
        # 验证状态已更新
        photo = self.session.query(Photo).filter_by(uuid="test-uuid-123").first()
        self.assertTrue(photo.marked_del)
        
        # 测试取消标记
        result = update_photo_marked_status(self.session, "test-uuid-123", False)
        self.assertTrue(result)
        
        # 验证状态已更新
        photo = self.session.query(Photo).filter_by(uuid="test-uuid-123").first()
        self.assertFalse(photo.marked_del)
        
        # 测试不存在的照片
        result = update_photo_marked_status(self.session, "non-existent", True)
        self.assertFalse(result)
    
    def test_get_marked_photos(self):
        """测试获取已标记照片"""
        # 初始状态：没有已标记照片
        marked_photos = get_marked_photos(self.session)
        self.assertEqual(len(marked_photos), 0)
        
        # 标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 获取已标记照片
        marked_photos = get_marked_photos(self.session)
        self.assertEqual(len(marked_photos), 1)
        self.assertEqual(marked_photos[0].uuid, "test-uuid-123")
    
    def test_get_unmarked_photos(self):
        """测试获取未标记照片"""
        # 初始状态：有一张未标记照片
        unmarked_photos = get_unmarked_photos(self.session)
        self.assertEqual(len(unmarked_photos), 1)
        
        # 标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 获取未标记照片
        unmarked_photos = get_unmarked_photos(self.session)
        self.assertEqual(len(unmarked_photos), 0)
    
    def test_get_photo_marked_status(self):
        """测试获取照片标记状态"""
        # 初始状态：未标记
        status = get_photo_marked_status(self.session, "test-uuid-123")
        self.assertFalse(status)
        
        # 标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 获取状态
        status = get_photo_marked_status(self.session, "test-uuid-123")
        self.assertTrue(status)
        
        # 测试不存在的照片
        status = get_photo_marked_status(self.session, "non-existent")
        self.assertFalse(status)
    
    def test_get_marked_photos_count(self):
        """测试获取已标记照片数量"""
        # 初始状态：0张已标记照片
        count = get_marked_photos_count(self.session)
        self.assertEqual(count, 0)
        
        # 标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 获取数量
        count = get_marked_photos_count(self.session)
        self.assertEqual(count, 1)
    
    def test_filter_photos_by_marked_status(self):
        """测试照片过滤功能"""
        # 创建测试照片列表
        photos = [
            PhotoInfo(uuid="photo1", filename="photo1.jpg", marked_del=False),
            PhotoInfo(uuid="photo2", filename="photo2.jpg", marked_del=True),
            PhotoInfo(uuid="photo3", filename="photo3.jpg", marked_del=False),
        ]
        
        # 测试显示所有照片
        filtered = filter_photos_by_marked_status(photos, show_marked=True)
        self.assertEqual(len(filtered), 3)
        
        # 测试只显示未标记照片
        filtered = filter_photos_by_marked_status(photos, show_marked=False)
        self.assertEqual(len(filtered), 2)
        self.assertFalse(any(photo.marked_del for photo in filtered))
    
    @patch('photo_dedup.collector.add_photo_to_marked_delete_album')
    def test_mark_photo_for_deletion(self, mock_album_add):
        """测试标记照片删除功能"""
        mock_album_add.return_value = True
        
        # 测试标记照片
        result = mark_photo_for_deletion("test-uuid-123")
        self.assertTrue(result)
        
        # 验证数据库状态
        photo = self.session.query(Photo).filter_by(uuid="test-uuid-123").first()
        self.assertTrue(photo.marked_del)
        
        # 验证相册操作被调用
        mock_album_add.assert_called_once_with("test-uuid-123")
    
    @patch('photo_dedup.collector.remove_photo_from_marked_delete_album')
    def test_unmark_photo_for_deletion(self, mock_album_remove):
        """测试取消标记照片删除功能"""
        mock_album_remove.return_value = True
        
        # 先标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 测试取消标记
        result = unmark_photo_for_deletion("test-uuid-123")
        self.assertTrue(result)
        
        # 验证数据库状态
        photo = self.session.query(Photo).filter_by(uuid="test-uuid-123").first()
        self.assertFalse(photo.marked_del)
        
        # 验证相册操作被调用
        mock_album_remove.assert_called_once_with("test-uuid-123")
    
    def test_get_photo_mark_status(self):
        """测试获取照片标记状态（collector模块）"""
        # 初始状态：未标记
        status = get_photo_mark_status("test-uuid-123")
        self.assertFalse(status)
        
        # 标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 获取状态
        status = get_photo_mark_status("test-uuid-123")
        self.assertTrue(status)
    
    def test_get_marked_photos_summary(self):
        """测试获取已标记照片摘要"""
        # 初始状态：没有已标记照片
        summary = get_marked_photos_summary()
        self.assertEqual(summary["marked_count"], 0)
        self.assertEqual(len(summary["marked_photos"]), 0)
        
        # 标记照片
        update_photo_marked_status(self.session, "test-uuid-123", True)
        
        # 获取摘要
        summary = get_marked_photos_summary()
        self.assertEqual(summary["marked_count"], 1)
        self.assertEqual(len(summary["marked_photos"]), 1)
        self.assertEqual(summary["marked_photos"][0]["uuid"], "test-uuid-123")


if __name__ == '__main__':
    unittest.main()
